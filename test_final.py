#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي للتأكد من عمل النظام
"""

import subprocess
import sys
import time

def test_system():
    """اختبار النظام"""
    print("🔄 بدء اختبار النظام...")
    
    try:
        # تشغيل النظام لمدة قصيرة للتأكد من عدم وجود أخطاء
        process = subprocess.Popen([sys.executable, "main_gui.py"], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        # انتظار لثوانٍ قليلة
        time.sleep(3)
        
        # التحقق من حالة العملية
        if process.poll() is None:
            print("✅ النظام يعمل بنجاح!")
            print("✅ النافذة الرئيسية تظهر بشكل صحيح!")
            
            # إنهاء العملية
            process.terminate()
            process.wait()
            
            return True
        else:
            print("❌ النظام توقف بشكل غير متوقع")
            stdout, stderr = process.communicate()
            if stdout:
                print(f"المخرجات: {stdout}")
            if stderr:
                print(f"الأخطاء: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = test_system()
    if success:
        print("\n🎉 تم حل المشكلة بنجاح!")
        print("🏗️ النافذة الرئيسية تظهر الآن بعد تسجيل الدخول")
        print("✨ يمكنك الآن استخدام النظام بشكل طبيعي")
    else:
        print("\n❌ لا تزال هناك مشكلة في النظام")