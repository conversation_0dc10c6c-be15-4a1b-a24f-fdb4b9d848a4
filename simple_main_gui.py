#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة من الواجهة الرئيسية لاختبار المشكلة
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from engineering_management_system import DatabaseManager, AuthenticationManager
import logging

class SimpleLoginWindow:
    """نافذة تسجيل الدخول المبسطة"""
    
    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        self.window = None

    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.window = ttk_bs.Window(
            title="تسجيل الدخول",
            themename="cosmo",
            size=(400, 300)
        )

        # توسيط النافذة
        self.center_window()

        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="🏗️ نظام إدارة أعمال الإدارة الهندسية",
            font=("Segoe UI", 14, "bold")
        )
        title_label.pack(pady=20)

        # حقل اسم المستخدم
        ttk_bs.Label(main_frame, text="اسم المستخدم:").pack(pady=5)
        self.username_entry = ttk_bs.Entry(main_frame, width=30)
        self.username_entry.pack(pady=5)
        self.username_entry.insert(0, "admin")

        # حقل كلمة المرور
        ttk_bs.Label(main_frame, text="كلمة المرور:").pack(pady=5)
        self.password_entry = ttk_bs.Entry(main_frame, width=30, show="*")
        self.password_entry.pack(pady=5)
        self.password_entry.insert(0, "admin123")

        # زر تسجيل الدخول
        login_btn = ttk_bs.Button(
            main_frame,
            text="🚀 تسجيل الدخول",
            bootstyle="success",
            command=self.login
        )
        login_btn.pack(pady=20)
        
        # ربط مفتاح Enter
        self.window.bind('<Return>', lambda e: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
        
        self.window.mainloop()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        print(f"محاولة تسجيل الدخول: {username}")
        
        if self.auth_manager.authenticate(username, password):
            print("تسجيل الدخول نجح!")
            messagebox.showinfo("نجح", f"مرحباً {self.auth_manager.current_user['full_name']}")
            
            # إغلاق نافذة تسجيل الدخول
            self.window.destroy()
            
            # استدعاء النافذة الرئيسية
            print("استدعاء النافذة الرئيسية...")
            self.on_success_callback()
        else:
            print("فشل تسجيل الدخول!")
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")

class SimpleMainApplication:
    """التطبيق الرئيسي المبسط"""
    
    def __init__(self):
        self.db_manager = DatabaseManager("data/engineering_system.db")
        self.auth_manager = AuthenticationManager(self.db_manager)
        self.window = None

    def start(self):
        """بدء التطبيق"""
        print("بدء التطبيق...")
        login_window = SimpleLoginWindow(self.auth_manager, self.show_main_window)
        login_window.show()
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        print("بدء إنشاء النافذة الرئيسية...")
        
        try:
            self.window = ttk_bs.Window(
                title="🏗️ النافذة الرئيسية - نظام إدارة أعمال الإدارة الهندسية",
                themename="cosmo",
                size=(1000, 700)
            )
            
            print("تم إنشاء النافذة...")

            # توسيط النافذة
            self.center_window()
            print("تم توسيط النافذة...")
            
            # التأكد من ظهور النافذة
            self.window.lift()
            self.window.focus_force()
            print("تم رفع النافذة...")

            # إنشاء المحتوى
            self.create_content()
            print("تم إنشاء المحتوى...")

            print("بدء mainloop...")
            self.window.mainloop()
            
        except Exception as e:
            print(f"خطأ في النافذة الرئيسية: {e}")
            import traceback
            traceback.print_exc()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 1000
        height = 700
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def create_content(self):
        """إنشاء محتوى النافذة الرئيسية"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان ترحيبي
        welcome_label = ttk_bs.Label(
            main_frame,
            text=f"🏗️ مرحباً {self.auth_manager.current_user['full_name']} 🏗️",
            font=("Segoe UI", 18, "bold"),
            bootstyle="primary"
        )
        welcome_label.pack(pady=30)
        
        # معلومات المستخدم
        user_info = f"👤 المستخدم: {self.auth_manager.current_user['full_name']} | 🔐 الصلاحية: {self.auth_manager.current_user['role']}"
        info_label = ttk_bs.Label(
            main_frame,
            text=user_info,
            font=("Segoe UI", 12),
            bootstyle="info"
        )
        info_label.pack(pady=10)
        
        # رسالة نجاح
        success_label = ttk_bs.Label(
            main_frame,
            text="✅ تم تسجيل الدخول بنجاح! النافذة الرئيسية تعمل بشكل صحيح.",
            font=("Segoe UI", 14),
            bootstyle="success"
        )
        success_label.pack(pady=20)
        
        # أزرار اختبار
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(pady=30)
        
        ttk_bs.Button(
            buttons_frame,
            text="📋 اختبار رسالة",
            command=lambda: messagebox.showinfo("اختبار", "النافذة الرئيسية تعمل!"),
            bootstyle="primary"
        ).pack(side=LEFT, padx=10)
        
        ttk_bs.Button(
            buttons_frame,
            text="🚪 تسجيل الخروج",
            command=self.logout,
            bootstyle="secondary"
        ).pack(side=LEFT, padx=10)
        
        ttk_bs.Button(
            buttons_frame,
            text="❌ إغلاق",
            command=self.window.destroy,
            bootstyle="danger"
        ).pack(side=LEFT, padx=10)

    def logout(self):
        """تسجيل الخروج"""
        self.auth_manager.logout()
        self.window.destroy()
        self.start()

if __name__ == "__main__":
    print("تشغيل النسخة المبسطة...")
    app = SimpleMainApplication()
    app.start()