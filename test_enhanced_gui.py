#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار الواجهة المحسنة
Test Enhanced GUI
"""

import sys
import os

# إضافة المسار الحالي لـ Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """تشغيل التطبيق المحسن"""
    try:
        print("🚀 بدء تشغيل نظام إدارة أعمال الإدارة الهندسية المحسن...")
        print("✨ التحسينات المطبقة:")
        print("   - جميع الشاشات تفتح في وسط الشاشة")
        print("   - خطوط أكبر وأوضح")
        print("   - ألوان وأشكال مميزة")
        print("   - تذييل محسن مع الوقت والتاريخ")
        print("   - خط أزرق غامق وثقيل")
        print("=" * 50)
        
        from main_gui import MainApplication
        app = MainApplication()
        app.start()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("تأكد من وجود جميع الملفات المطلوبة")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()