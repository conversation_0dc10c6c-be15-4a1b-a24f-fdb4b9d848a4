#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل آمن للنظام المحسن
Safe System Launcher
"""

import sys
import os
import traceback

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """تشغيل النظام بطريقة آمنة"""
    try:
        print("🚀 بدء تشغيل نظام إدارة أعمال الإدارة الهندسية المحسن...")
        print("✨ الإصدار: 2.0 Enhanced")
        print("=" * 60)
        
        # استيراد وتشغيل النظام
        from main_gui import MainApplication
        
        print("✅ تم تحميل النظام بنجاح")
        print("🎯 جاري فتح الواجهة الرئيسية...")
        print("=" * 60)
        
        app = MainApplication()
        app.start()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("\n🔧 الحلول المقترحة:")
        print("1. تأكد من تثبيت Python بشكل صحيح")
        print("2. تثبيت المكتبات المطلوبة:")
        print("   pip install ttkbootstrap")
        print("3. تأكد من وجود جميع الملفات في نفس المجلد")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("\n🔍 تفاصيل الخطأ:")
        traceback.print_exc()
        print("\n💡 إذا استمرت المشكلة، تواصل مع فريق الدعم الفني")
    
    finally:
        print("\n👋 شكراً لاستخدام النظام!")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()