"""
وحدة إدارة المشاريع الهندسية - نسخة مبسطة ومحسنة
Simple Project Management Module - Enhanced Version
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import ttkbootstrap as ttk_bs
from datetime import datetime

class SimpleProjectManagementWindow:
    """نافذة إدارة المشاريع المبسطة"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.window = None
        self.projects_tree = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط المحسنة"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.tree_font = ("Segoe UI", 11)

    def show(self):
        """عرض نافذة إدارة المشاريع"""
        # Check if window already exists and is open
        if hasattr(self, 'window') and self.window is not None and self.window.winfo_exists():
            self.window.lift()
            return

        self.window = tk.Toplevel(self.parent)
        self.window.title("🏗️ إدارة المشاريع الهندسية")
        self.window.geometry("1100x650")
        self.window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        self.create_interface()
        self.load_projects()
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة إدارة المشاريع المحسنة"""
        # عنوان الصفحة
        header_frame = ttk_bs.Frame(self.window)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        title_label = ttk_bs.Label(
            header_frame,
            text="🏗️ إدارة المشاريع الهندسية 🏗️",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack(side=tk.LEFT)

        # شريط الأدوات المحسن
        toolbar_frame = ttk_bs.LabelFrame(
            self.window,
            text="🛠️ أدوات المشاريع",
            padding=15,
            bootstyle="info"
        )
        toolbar_frame.pack(fill=tk.X, padx=15, pady=10)

        # الأزرار مع أيقونات وتصميم محسن
        ttk_bs.Button(
            toolbar_frame,
            text="➕ مشروع جديد",
            command=self.add_project,
            bootstyle="success",
            width=18).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_project,
            bootstyle="warning",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_project,
            bootstyle="danger",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="📋 تفاصيل",
            command=self.view_project_details,
            bootstyle="info",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🔄 تحديث",
            command=self.load_projects,
            bootstyle="secondary",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🖨️ طباعة PDF",
            command=self.print_projects_report,
            bootstyle="primary",
            width=18).pack(side=tk.LEFT, padx=8, ipady=8)
        
        # شريط البحث المحسن
        search_frame = ttk_bs.LabelFrame(
            self.window,
            text="🔍 البحث والفلترة",
            padding=15,
            bootstyle="secondary"
        )
        search_frame.pack(fill=tk.X, padx=15, pady=10)

        ttk_bs.Label(
            search_frame,
            text="🔍 البحث:"
        ).pack(side=tk.LEFT, padx=(0, 5))

        self.search_entry = ttk_bs.Entry(
            search_frame,
            width=40,
            bootstyle="info"
        )
        self.search_entry.pack(side=tk.LEFT, padx=5, ipady=3)
        self.search_entry.bind('<KeyRelease>', self.filter_projects)

        ttk_bs.Button(
            search_frame,
            text="🔍 بحث",
            command=self.filter_projects,
            bootstyle="info",
            width=10
        ).pack(side=tk.LEFT, padx=5, ipady=3)
        
        # جدول المشاريع المحسن
        tree_frame = ttk_bs.LabelFrame(
            self.window,
            text="📋 قائمة المشاريع",
            padding=15,
            bootstyle="primary"
        )
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # إنشاء Treeview مع تحسينات
        columns = ("id", "name", "location", "type", "cost", "contractor",
                  "start_date", "end_date", "status", "progress")
        self.projects_tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            height=18
        )

        # تطبيق خط محسن على الجدول
        style = ttk.Style()
        style.configure("Treeview", rowheight=30)
        style.configure("Treeview.Heading", foreground="#1e3a8a")
        
        # تعريف العناوين المحسنة مع أيقونات
        headers = {
            "id": "🆔 المعرف",
            "name": "🏗️ اسم المشروع",
            "location": "📍 الموقع",
            "type": "🏢 النوع",
            "cost": "💰 الكلفة",
            "contractor": "👷 المقاول",
            "start_date": "📅 تاريخ البداية",
            "end_date": "📅 تاريخ الانتهاء",
            "status": "📊 الحالة",
            "progress": "📈 نسبة الإنجاز"
        }
        
        for col, header in headers.items():
            self.projects_tree.heading(col, text=header)
            if col == "id":
                self.projects_tree.column(col, width=50)
            elif col in ["name", "location", "contractor"]:
                self.projects_tree.column(col, width=150)
            else:
                self.projects_tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar_y = tk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.projects_tree.yview)
        scrollbar_x = tk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.projects_tree.xview)
        self.projects_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تعبئة الجدول وشريط التمرير
        self.projects_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # ربط النقر المزدوج
        self.projects_tree.bind('<Double-1>', lambda e: self.view_project_details())
    
    def load_projects(self):
        """تحميل المشاريع من قاعدة البيانات"""
        if not self.projects_tree:
            return
            
        # مسح البيانات الحالية
        for item in self.projects_tree.get_children():
            self.projects_tree.delete(item)
        
        # تحميل البيانات الجديدة
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        status_map = {
            "planning": "تخطيط",
            "execution": "قيد التنفيذ",
            "completed": "مكتمل",
            "delivery": "مؤجل",
            "tender": "ملغي"
        }
        
        try:
            cursor.execute('''
                SELECT id, name, location, project_type, cost, contractor, 
                       start_date, end_date, status, progress_percentage
                FROM projects
                ORDER BY created_at DESC
            ''')
            
            projects = cursor.fetchall()
            
            for project in projects:
                # تنسيق البيانات
                project_id, name, location, project_type, cost, contractor, start_date, end_date, status, progress = project
                
                # تنسيق الكلفة
                cost_formatted = f"{cost:,.0f}" if cost else ""
                
                # تنسيق نسبة الإنجاز
                progress_formatted = f"{progress}%" if progress is not None else "0%"
                
                # تحويل الحالة إلى العربية
                status_ar = status_map.get(status, status)
                
                # إدراج في الجدول
                self.projects_tree.insert('', 'end', values=(
                    project_id, name or "", location or "", project_type or "",
                    cost_formatted, contractor or "", start_date or "", end_date or "",
                    status_ar or "", progress_formatted
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المشاريع: {str(e)}")
        finally:
            conn.close()
    
    def filter_projects(self, event=None):
        """فلترة المشاريع حسب النص المدخل"""
        search_text = self.search_entry.get().lower()
        
        # مسح البيانات الحالية
        for item in self.projects_tree.get_children():
            self.projects_tree.delete(item)
        
        # تحميل البيانات المفلترة
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            if search_text:
                cursor.execute('''
                    SELECT id, name, location, project_type, cost, contractor, 
                           start_date, end_date, status, progress_percentage
                    FROM projects
                    WHERE LOWER(name) LIKE ? OR LOWER(location) LIKE ? OR LOWER(contractor) LIKE ?
                    ORDER BY created_at DESC
                ''', (f'%{search_text}%', f'%{search_text}%', f'%{search_text}%'))
            else:
                cursor.execute('''
                    SELECT id, name, location, project_type, cost, contractor, 
                           start_date, end_date, status, progress_percentage
                    FROM projects
                    ORDER BY created_at DESC
                ''')
            
            projects = cursor.fetchall()
            
            for project in projects:
                project_id, name, location, project_type, cost, contractor, start_date, end_date, status, progress = project
                
                cost_formatted = f"{cost:,.0f}" if cost else ""
                progress_formatted = f"{progress}%" if progress is not None else "0%"
                
                self.projects_tree.insert('', 'end', values=(
                    project_id, name or "", location or "", project_type or "",
                    cost_formatted, contractor or "", start_date or "", end_date or "",
                    status or "", progress_formatted
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
        finally:
            conn.close()
    
    def add_project(self):
        """إضافة مشروع جديد"""
        dialog = SimpleProjectDialog(self.window, self.db_manager, self.auth_manager, "إضافة مشروع جديد")
        if dialog.show():
            self.load_projects()
    
    def edit_project(self):
        """تعديل مشروع محدد"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للتعديل")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        
        # جلب بيانات المشروع
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT * FROM projects WHERE id = ?', (project_id,))
            project_data = cursor.fetchone()
            
            if project_data:
                # تحويل إلى قاموس
                columns = [description[0] for description in cursor.description]
                project_dict = dict(zip(columns, project_data))
                
                dialog = SimpleProjectDialog(self.window, self.db_manager, self.auth_manager, "تعديل المشروع", project_dict)
                if dialog.show():
                    self.load_projects()
                # Keep the window visible after editing
                self.window.deiconify()
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على المشروع")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في جلب بيانات المشروع: {str(e)}")
        finally:
            conn.close()
    
    def delete_project(self):
        """حذف مشروع محدد"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للحذف")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        project_name = self.projects_tree.item(selected[0])['values'][1]
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المشروع '{project_name}'؟"):
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute('DELETE FROM projects WHERE id = ?', (project_id,))
                conn.commit()
                messagebox.showinfo("نجح", "تم حذف المشروع بنجاح")
                self.load_projects()
                # Keep the window visible after deletion
                self.window.deiconify()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المشروع: {str(e)}")
            finally:
                conn.close()
    
    def view_project_details(self):
        """عرض تفاصيل المشروع"""
        if hasattr(self, 'details_window') and self.details_window.winfo_exists():
            # If details window is already open, bring it to front
            self.details_window.lift()
            return

        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لعرض التفاصيل")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]

        # Create a new Toplevel window for details
        self.details_window = tk.Toplevel(self.window)
        self.details_window.title(f"تفاصيل المشروع رقم {project_id}")
        self.details_window.geometry("600x400")
        self.details_window.resizable(False, False)

        # Center the details window
        self.details_window.update_idletasks()
        x = (self.details_window.winfo_screenwidth() // 2) - (self.details_window.winfo_width() // 2)
        y = (self.details_window.winfo_screenheight() // 2) - (self.details_window.winfo_height() // 2)
        self.details_window.geometry(f"+{x}+{y}")

        # Fetch project details from database
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('SELECT * FROM projects WHERE id = ?', (project_id,))
            project_data = cursor.fetchone()
            if not project_data:
                messagebox.showerror("خطأ", "لم يتم العثور على بيانات المشروع")
                self.details_window.destroy()
                return

            columns = [description[0] for description in cursor.description]
            project_dict = dict(zip(columns, project_data))

            # Display project details in the window
            frame = ttk_bs.Frame(self.details_window, padding=10)
            frame.pack(fill=tk.BOTH, expand=True)

            for key, value in project_dict.items():
                label = ttk_bs.Label(frame, text=f"{key}: {value}", anchor="w")
                label.pack(fill=tk.X, pady=2)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في جلب بيانات المشروع: {str(e)}")
            self.details_window.destroy()
        finally:
            conn.close()


class SimpleProjectDialog:
    """نافذة إضافة/تعديل مشروع مبسطة ومحسنة"""

    def __init__(self, parent, db_manager, auth_manager, title, project_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.title = title
        self.project_data = project_data
        self.result = False
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط المحسنة"""
        self.title_font = ("Segoe UI", 10, "bold")
        self.header_font = ("Segoe UI", 9, "bold")
        self.normal_font = ("Segoe UI", 8)
        self.button_font = ("Segoe UI", 7, "bold")
        self.label_font = ("Segoe UI", 7)

    def show(self):
        """عرض النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("950x800")
        self.window.resizable(False, False)  # Fixed size window as requested
        self.window.transient(self.parent)
        self.window.grab_set()
        
        self.create_form()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
        
        self.window.wait_window()
        return self.result
    
    def create_form(self):
        """إنشاء نموذج المشروع المحسن"""
        # إطار رئيسي مع تصميم محسن
        main_frame = ttk_bs.Frame(self.window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # إضافة إطار قابل للتمرير
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk_bs.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk_bs.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # عنوان النموذج
        title_frame = ttk_bs.Frame(scrollable_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = ttk_bs.Label(
            title_frame,
            text=f"📝 {self.title} 📝",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack()

        # إطار أفقي لمجموعتي البيانات الأساسية وتفاصيل المشروع
        top_row_frame = ttk_bs.Frame(scrollable_frame)
        top_row_frame.pack(fill=tk.X, pady=(0, 15))

        # إطار البيانات الأساسية
        basic_frame = ttk_bs.LabelFrame(
            top_row_frame,
            text="📋 البيانات الأساسية",
            padding=20,
            bootstyle="info"
        )
        basic_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # اسم المشروع
        ttk_bs.Label(
            basic_frame,
            text="🏗️ اسم المشروع: *"
        ).pack(anchor=tk.W, pady=(0, 5))

        self.name_entry = ttk_bs.Entry(
            basic_frame,
            width=60,
            bootstyle="info"
        )
        self.name_entry.pack(pady=(0, 15), ipady=5)

        # وصف المشروع
        ttk_bs.Label(
            basic_frame,
            text="📄 وصف المشروع:"
        ).pack(anchor=tk.W, pady=(0, 5))

        self.description_text = tk.Text(
            basic_frame,
            height=4,
            width=60,
            wrap=tk.WORD
        )
        self.description_text.pack(pady=(0, 15), ipady=5)

        # إطار تفاصيل المشروع
        location_frame = ttk_bs.LabelFrame(
            top_row_frame,
            text="📍 تفاصيل المشروع",
            padding=20,
            bootstyle="secondary"
        )
        location_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # موقع المشروع
        ttk_bs.Label(
            location_frame,
            text="📍 موقع المشروع:"
        ).pack(anchor=tk.W, pady=(0, 5))

        self.location_entry = ttk_bs.Entry(
            location_frame,
            width=60,
            bootstyle="secondary"
        )
        self.location_entry.pack(pady=(0, 15), ipady=5)

        # نوع المشروع
        ttk_bs.Label(
            location_frame,
            text="🏢 نوع المشروع:"
        ).pack(anchor=tk.W, pady=(0, 5))

        self.type_entry = ttk_bs.Entry(
            location_frame,
            width=60,
            bootstyle="secondary"
        )
        self.type_entry.pack(pady=(0, 15), ipady=5)

        # إطار أفقي لمجموعتي البيانات المالية والتواريخ والحالة
        bottom_row_frame = ttk_bs.Frame(scrollable_frame)
        bottom_row_frame.pack(fill=tk.X, pady=(0, 15))

        # إطار البيانات المالية والتعاقدية
        financial_frame = ttk_bs.LabelFrame(
            bottom_row_frame,
            text="💰 البيانات المالية والتعاقدية",
            padding=20,
            bootstyle="warning"
        )
        financial_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # الكلفة
        ttk_bs.Label(
            financial_frame,
            text="💰 الكلفة المقدرة:"
        ).pack(anchor=tk.W, pady=(0, 5))

        self.cost_entry = ttk_bs.Entry(
            financial_frame,
            width=60,
            bootstyle="warning"
        )
        self.cost_entry.pack(pady=(0, 15), ipady=5)

        # المقاول
        ttk_bs.Label(
            financial_frame,
            text="👷 المقاول:"
        ).pack(anchor=tk.W, pady=(0, 5))

        self.contractor_entry = ttk_bs.Entry(
            financial_frame,
            width=60,
            bootstyle="warning"
        )
        self.contractor_entry.pack(pady=(0, 15), ipady=5)

        # إطار التواريخ والحالة
        dates_frame = ttk_bs.LabelFrame(
            bottom_row_frame,
            text="📅 التواريخ والحالة",
            padding=20,
            bootstyle="success"
        )
        dates_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # تاريخ البداية
        ttk_bs.Label(
            dates_frame,
            text="📅 تاريخ البداية:"
        ).pack(anchor=tk.W, pady=(0, 5))

        self.start_date_entry = ttk_bs.Entry(
            dates_frame,
            width=60,
            bootstyle="success"
        )
        self.start_date_entry.pack(pady=(0, 15), ipady=5)

        # تاريخ النهاية المتوقع
        ttk_bs.Label(
            dates_frame,
            text="📅 تاريخ النهاية المتوقع:"
        ).pack(anchor=tk.W, pady=(0, 5))

        self.end_date_entry = ttk_bs.Entry(
            dates_frame,
            width=60,
            bootstyle="success"
        )
        self.end_date_entry.pack(pady=(0, 15), ipady=5)

        # حالة المشروع
        ttk_bs.Label(
            dates_frame,
            text="📊 حالة المشروع:"
        ).pack(anchor=tk.W, pady=(0, 5))

        self.status_combo = ttk_bs.Combobox(
            dates_frame,
            width=57,
            bootstyle="success"
        )
        self.status_combo['values'] = ("تخطيط", "قيد التنفيذ", "مكتمل", "مؤجل", "ملغي")
        self.status_combo.pack(pady=(0, 15), ipady=5)

        # نسبة الإنجاز
        ttk_bs.Label(
            dates_frame,
            text="📈 نسبة الإنجاز (%):"
        ).pack(anchor=tk.W, pady=(0, 5))

        self.progress_entry = ttk_bs.Entry(
            dates_frame,
            width=60,
            bootstyle="success"
        )
        self.progress_entry.pack(pady=(0, 15), ipady=5)
        
        # ملء البيانات إذا كان تعديل
        if self.project_data:
            self.name_entry.insert(0, self.project_data.get('name', ''))
            self.description_text.insert('1.0', self.project_data.get('description', '') or '')
            self.location_entry.insert(0, self.project_data.get('location', '') or '')
            self.type_entry.insert(0, self.project_data.get('project_type', '') or '')
            self.cost_entry.insert(0, str(self.project_data.get('cost', '') or ''))
            self.contractor_entry.insert(0, self.project_data.get('contractor', '') or '')
            self.start_date_entry.insert(0, self.project_data.get('start_date', '') or '')
            self.end_date_entry.insert(0, self.project_data.get('end_date', '') or '')
            # Reverse mapping for status from English code to Arabic string
            status_reverse_map = {
                "planning": "تخطيط",
                "execution": "قيد التنفيذ",
                "completed": "مكتمل",
                "delivery": "مؤجل",
                "tender": "ملغي"
            }
            status_code = self.project_data.get('status', 'planning')
            status_ar = status_reverse_map.get(status_code, None)
            if status_ar and status_ar in self.status_combo['values']:
                self.status_combo.set(status_ar)
            else:
                # fallback to first value if mapping fails
                self.status_combo.set(self.status_combo['values'][0])
            self.progress_entry.insert(0, str(self.project_data.get('progress_percentage', '') or '0'))
        
        # الأزرار المحسنة
        button_frame = ttk_bs.Frame(scrollable_frame)
        button_frame.pack(fill=tk.X, pady=25)

        ttk_bs.Button(
            button_frame,
            text="💾 حفظ",
            command=self.save_project,
            bootstyle="success",
            width=18).pack(side=tk.RIGHT, padx=(15, 0), ipady=10)

        ttk_bs.Button(
            button_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bootstyle="secondary",
            width=18).pack(side=tk.RIGHT, ipady=10)
    
    def save_project(self):
        """حفظ بيانات المشروع"""
        # التحقق من صحة البيانات
        name = self.name_entry.get().strip()
        description = self.description_text.get('1.0', tk.END).strip()
        location = self.location_entry.get().strip()
        project_type = self.type_entry.get().strip()
        cost_str = self.cost_entry.get().strip()
        contractor = self.contractor_entry.get().strip()
        start_date = self.start_date_entry.get().strip()
        end_date = self.end_date_entry.get().strip()
        status_ar = self.status_combo.get() or 'تخطيط'
        progress_str = self.progress_entry.get().strip()
        
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
            return
        
        # تحويل الكلفة إلى رقم
        cost = None
        if cost_str:
            try:
                cost = float(cost_str)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال كلفة صحيحة")
                return

        # تحويل نسبة الإنجاز إلى رقم
        progress = 0
        if progress_str:
            try:
                progress = float(progress_str)
                if progress < 0 or progress > 100:
                    messagebox.showerror("خطأ", "نسبة الإنجاز يجب أن تكون بين 0 و 100")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال نسبة إنجاز صحيحة")
                return
        
        # تحويل الحالة من العربية إلى الإنجليزية
        status_map = {
            "تخطيط": "planning",
            "قيد التنفيذ": "execution",
            "مكتمل": "completed",
            "مؤجل": "delivery",
            "ملغي": "tender"
        }
        status = status_map.get(status_ar, "planning")
        
        # حفظ في قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            if self.project_data:  # تعديل
                cursor.execute('''
                    UPDATE projects 
                    SET name=?, description=?, location=?, project_type=?, cost=?,
                        contractor=?, start_date=?, end_date=?, status=?, 
                        progress_percentage=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                ''', (name, description or None, location or None, project_type or None,
                      cost, contractor or None, start_date or None, end_date or None,
                      status, progress, self.project_data['id']))
            else:  # إضافة جديد
                cursor.execute('''
                    INSERT INTO projects (name, description, location, project_type, cost,
                                        contractor, start_date, end_date, status, 
                                        progress_percentage, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (name, description or None, location or None, project_type or None,
                      cost, contractor or None, start_date or None, end_date or None,
                      status, progress, self.auth_manager.current_user['id']))
            
            conn.commit()
            messagebox.showinfo("نجح", "تم حفظ بيانات المشروع بنجاح")
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
        finally:
            conn.close()

    def print_projects_report(self):
        """طباعة تقرير المشاريع إلى PDF"""
        try:
            # التحقق من وجود مكتبة reportlab
            try:
                from reportlab.lib.pagesizes import A4
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib.units import cm
                from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
                from reportlab.lib import colors
                from reportlab.lib.enums import TA_CENTER, TA_RIGHT
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
                from datetime import datetime
                import os
                from tkinter import filedialog
            except ImportError:
                messagebox.showerror("خطأ", "مكتبة reportlab غير متوفرة. يرجى تثبيتها أولاً:\npip install reportlab")
                return

            # الحصول على بيانات المشاريع
            projects_data = self.get_projects_data_for_report()

            if not projects_data:
                messagebox.showwarning("تحذير", "لا توجد مشاريع لطباعتها")
                return

            # اختيار مكان الحفظ
            current_date = datetime.now().strftime("%Y-%m-%d")
            default_filename = f"تقرير_المشاريع_{current_date}.pdf"

            file_path = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                initialvalue=default_filename,
                title="حفظ تقرير المشاريع"
            )

            if not file_path:
                return

            # إنشاء ملف PDF
            self.create_projects_pdf_report(projects_data, file_path)

            messagebox.showinfo("نجح", f"تم إنشاء تقرير المشاريع بنجاح:\n{file_path}")

            # فتح الملف للطباعة
            try:
                os.startfile(file_path)
            except:
                pass

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {str(e)}")

    def get_projects_data_for_report(self):
        """الحصول على بيانات المشاريع للتقرير"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT name, project_type, status, progress_percentage, cost,
                       contractor, start_date, end_date, location, description
                FROM projects
                ORDER BY name
            ''')

            projects = cursor.fetchall()
            conn.close()

            return projects

        except Exception as e:
            print(f"خطأ في جلب بيانات المشاريع: {e}")
            return []

    def create_projects_pdf_report(self, projects_data, file_path):
        """إنشاء تقرير PDF للمشاريع"""
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT
        from datetime import datetime

        # إنشاء مستند PDF
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )

        # إنشاء الأنماط
        styles = getSampleStyleSheet()

        # نمط العنوان
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Title'],
            fontSize=18,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )

        # نمط العنوان الفرعي
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading1'],
            fontSize=14,
            spaceAfter=12,
            alignment=TA_RIGHT,
            textColor=colors.darkgreen
        )

        # قائمة العناصر
        story = []

        # العنوان الرئيسي
        story.append(Paragraph("🏗️ تقرير المشاريع الهندسية 🏗️", title_style))
        story.append(Spacer(1, 20))

        # التاريخ
        current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
        story.append(Paragraph(f"تاريخ التقرير: {current_time}", styles['Normal']))
        story.append(Spacer(1, 20))

        # إحصائيات سريعة
        total_projects = len(projects_data)
        completed_projects = len([p for p in projects_data if p['status'] == 'مكتمل'])
        active_projects = len([p for p in projects_data if p['status'] == 'نشط'])

        story.append(Paragraph("📊 إحصائيات المشاريع", heading_style))
        story.append(Paragraph(f"• إجمالي المشاريع: {total_projects}", styles['Normal']))
        story.append(Paragraph(f"• المشاريع المكتملة: {completed_projects}", styles['Normal']))
        story.append(Paragraph(f"• المشاريع النشطة: {active_projects}", styles['Normal']))
        story.append(Spacer(1, 20))

        # جدول المشاريع
        story.append(Paragraph("📋 تفاصيل المشاريع", heading_style))

        # إنشاء بيانات الجدول
        table_data = [['اسم المشروع', 'النوع', 'الحالة', 'نسبة الإنجاز', 'التكلفة']]

        for project in projects_data:
            table_data.append([
                project['name'] or '',
                project['project_type'] or '',
                project['status'] or '',
                f"{project['progress_percentage'] or 0}%",
                f"{project['cost'] or 0:,} ريال"
            ])

        # إنشاء الجدول
        table = Table(table_data, repeatRows=1)
        table.setStyle(TableStyle([
            # تنسيق الرأس
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

            # تنسيق البيانات
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        story.append(table)

        # بناء المستند
        doc.build(story)
