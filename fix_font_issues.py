#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل الخطوط في ttkbootstrap
Fix font issues in ttkbootstrap
"""

import re
import os

def fix_font_in_file(file_path):
    """إصلاح مشاكل الخطوط في ملف واحد"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إزالة font من ttk_bs.Button
        content = re.sub(r'(ttk_bs\.Button\([^)]*),\s*font=[^,)]+([^)]*\))', r'\1\2', content)
        
        # إزالة font من ttk_bs.Entry
        content = re.sub(r'(ttk_bs\.Entry\([^)]*),\s*font=[^,)]+([^)]*\))', r'\1\2', content)
        
        # إزالة font من ttk_bs.Combobox
        content = re.sub(r'(ttk_bs\.Combobox\([^)]*),\s*font=[^,)]+([^)]*\))', r'\1\2', content)
        
        # إزالة font من tk.Text
        content = re.sub(r'(tk\.Text\([^)]*),\s*font=[^,)]+([^)]*\))', r'\1\2', content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"تم إصلاح الملف: {file_path}")
        
    except Exception as e:
        print(f"خطأ في إصلاح الملف {file_path}: {str(e)}")

def main():
    """الدالة الرئيسية"""
    files_to_fix = [
        'buildings_management.py',
        'maintenance_management.py', 
        'reports_management.py',
        'simple_project_management.py'
    ]
    
    for file_name in files_to_fix:
        if os.path.exists(file_name):
            fix_font_in_file(file_name)
        else:
            print(f"الملف غير موجود: {file_name}")

if __name__ == "__main__":
    main()