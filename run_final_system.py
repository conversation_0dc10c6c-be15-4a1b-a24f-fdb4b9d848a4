#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام النهائي المحسن
Run Final Enhanced System
"""

import sys
import os

def main():
    """تشغيل النظام"""
    print("🚀 بدء تشغيل نظام إدارة أعمال الإدارة الهندسية المحسن...")
    print("=" * 60)
    print("🏗️ Engineering Management System - Enhanced Version")
    print("📋 جميع الواجهات تستخدم ttkbootstrap و tkinter")
    print("✨ النافذة الرئيسية تظهر بعد تسجيل الدخول")
    print("🔧 نوافذ محسنة لإدارة المشاريع والمباني والصيانة")
    print("=" * 60)
    
    try:
        # استيراد وتشغيل النظام
        from new_main_gui import EngineeringManagementApp
        
        app = EngineeringManagementApp()
        app.start()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من وجود جميع الملفات المطلوبة")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()