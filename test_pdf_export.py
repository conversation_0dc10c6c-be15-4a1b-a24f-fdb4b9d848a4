#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة تصدير PDF
Test PDF Export Feature
"""

import sys
import os

# إضافة المسار الحالي لـ Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_pdf_export():
    """اختبار تصدير PDF"""
    try:
        print("🔍 اختبار ميزة تصدير PDF...")
        
        # استيراد المكتبات المطلوبة
        from reports_management import ReportsManagementWindow
        from engineering_management_system import DatabaseManager, AuthenticationManager
        import tkinter as tk
        
        print("✅ تم استيراد المكتبات بنجاح")
        
        # إنشاء نافذة وهمية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # إنشاء مدير قاعدة البيانات ومدير المصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # تسجيل دخول وهمي
        auth_manager.authenticate("admin", "admin123")
        
        print("✅ تم إنشاء مدراء النظام بنجاح")
        
        # إنشاء نافذة التقارير
        reports_window = ReportsManagementWindow(root, db_manager, auth_manager)
        
        print("✅ تم إنشاء نافذة التقارير بنجاح")
        
        # اختبار إنشاء PDF
        test_content = """
📊 تقرير اختبار PDF

📅 تاريخ التقرير: 2025/07/02

📈 هذا تقرير اختبار لميزة تصدير PDF:
• تم تثبيت مكتبة reportlab بنجاح
• تم إضافة أزرار تصدير PDF
• تم إضافة وظائف تصدير PDF
• النظام جاهز للاستخدام

🎯 الميزات المتوفرة:
• تصدير التقرير الحالي إلى PDF
• تصدير جميع التقارير إلى PDF
• دعم اللغة العربية في PDF
• تنسيق احترافي للتقارير

✅ الاختبار مكتمل بنجاح!
        """
        
        # اختبار إنشاء PDF
        test_file = "test_report.pdf"
        reports_window.create_pdf_report(test_content, test_file, "تقرير اختبار")
        
        if os.path.exists(test_file):
            print(f"✅ تم إنشاء ملف PDF بنجاح: {test_file}")
            file_size = os.path.getsize(test_file)
            print(f"📄 حجم الملف: {file_size} بايت")
            
            # حذف ملف الاختبار
            os.remove(test_file)
            print("🗑️ تم حذف ملف الاختبار")
        else:
            print("❌ فشل في إنشاء ملف PDF")
            return False
        
        root.destroy()
        print("🎉 اختبار تصدير PDF مكتمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار PDF: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_pdf_export()
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
        print("🎯 ميزة تصدير PDF جاهزة للاستخدام")
        print("\n📋 كيفية الاستخدام:")
        print("1. افتح النظام الرئيسي")
        print("2. اذهب إلى قائمة التقارير")
        print("3. اختر 'لوحة التحكم'")
        print("4. اذهب إلى تبويب 'تقارير مفصلة'")
        print("5. اختر نوع التقرير المطلوب")
        print("6. اضغط على 'تصدير التقرير الحالي إلى PDF'")
        print("7. أو اضغط على 'تصدير جميع التقارير إلى PDF'")
    else:
        print("\n❌ فشل في الاختبار!")
        print("يرجى التحقق من تثبيت مكتبة reportlab")
