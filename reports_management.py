"""
وحدة إدارة التقارير
Reports Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib
matplotlib.use('TkAgg')

class ReportsManagementWindow:
    """نافذة إدارة التقارير"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")

    def show(self):
        """عرض نافذة التقارير"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📊 لوحة التحكم والتقارير")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        self.create_interface()
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة التقارير"""
        # عنوان الصفحة
        header_frame = ttk_bs.Frame(self.window)
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        title_label = ttk_bs.Label(
            header_frame,
            text="📊 لوحة التحكم والتقارير 📊",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack()

        # إطار الإحصائيات السريعة
        stats_frame = ttk_bs.LabelFrame(
            self.window,
            text="📈 الإحصائيات السريعة",
            padding=20,
            bootstyle="info"
        )
        stats_frame.pack(fill=tk.X, padx=15, pady=10)

        # إنشاء بطاقات الإحصائيات
        stats_container = ttk_bs.Frame(stats_frame)
        stats_container.pack(fill=tk.X)

        self.create_stat_card(stats_container, "🏗️ المشاريع النشطة", "12", "success", 0)
        self.create_stat_card(stats_container, "🏢 المباني", "8", "info", 1)
        self.create_stat_card(stats_container, "🔧 بلاغات الصيانة", "25", "warning", 2)
        self.create_stat_card(stats_container, "✅ المهام المكتملة", "45", "primary", 3)

        # إطار التقارير والرسوم البيانية
        reports_frame = ttk_bs.LabelFrame(
            self.window,
            text="📊 التقارير والرسوم البيانية",
            padding=20,
            bootstyle="secondary"
        )
        reports_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # إنشاء Notebook للتبويبات
        notebook = ttk_bs.Notebook(reports_frame, bootstyle="info")
        notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب المشاريع
        projects_tab = ttk_bs.Frame(notebook)
        notebook.add(projects_tab, text="📈 تقارير المشاريع")
        self.create_projects_chart(projects_tab)

        # تبويب الصيانة
        maintenance_tab = ttk_bs.Frame(notebook)
        notebook.add(maintenance_tab, text="🔧 تقارير الصيانة")
        self.create_maintenance_chart(maintenance_tab)

        # تبويب المباني
        buildings_tab = ttk_bs.Frame(notebook)
        notebook.add(buildings_tab, text="🏢 تقارير المباني")
        self.create_buildings_chart(buildings_tab)

        # تبويب التقارير المفصلة
        detailed_tab = ttk_bs.Frame(notebook)
        notebook.add(detailed_tab, text="📋 تقارير مفصلة")
        self.create_detailed_reports(detailed_tab)

    def create_stat_card(self, parent, title, value, style, column):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk_bs.LabelFrame(
            parent,
            text=title,
            padding=15,
            bootstyle=style
        )
        card_frame.grid(row=0, column=column, padx=10, pady=5, sticky="ew")
        parent.grid_columnconfigure(column, weight=1)

        value_label = ttk_bs.Label(
            card_frame,
            text=value,
            bootstyle=style,
            foreground="#1e3a8a"
        )
        value_label.pack()

    def create_projects_chart(self, parent):
        """إنشاء رسم بياني للمشاريع"""
        chart_frame = ttk_bs.Frame(parent, padding=10)
        chart_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الرسم البياني
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # رسم بياني دائري لحالة المشاريع
        project_status = ['مكتمل', 'قيد التنفيذ', 'مؤجل', 'ملغي']
        project_counts = [8, 12, 3, 2]
        colors = ['#28a745', '#ffc107', '#fd7e14', '#dc3545']
        
        ax1.pie(project_counts, labels=project_status, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('توزيع المشاريع حسب الحالة', fontsize=14, fontweight='bold')

        # رسم بياني عمودي للمشاريع حسب النوع
        project_types = ['سكني', 'تجاري', 'إداري', 'صناعي']
        type_counts = [15, 8, 12, 5]
        
        ax2.bar(project_types, type_counts, color=['#007bff', '#28a745', '#ffc107', '#dc3545'])
        ax2.set_title('المشاريع حسب النوع', fontsize=14, fontweight='bold')
        ax2.set_ylabel('عدد المشاريع')

        plt.tight_layout()

        # إدراج الرسم في النافذة
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_maintenance_chart(self, parent):
        """إنشاء رسم بياني للصيانة"""
        chart_frame = ttk_bs.Frame(parent, padding=10)
        chart_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الرسم البياني
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # رسم بياني دائري لحالة بلاغات الصيانة
        maintenance_status = ['مكتمل', 'قيد التنفيذ', 'جديد', 'مؤجل']
        maintenance_counts = [15, 8, 12, 5]
        colors = ['#28a745', '#ffc107', '#007bff', '#fd7e14']
        
        ax1.pie(maintenance_counts, labels=maintenance_status, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('توزيع بلاغات الصيانة حسب الحالة', fontsize=14, fontweight='bold')

        # رسم بياني عمودي لأنواع الأعطال
        fault_types = ['كهرباء', 'سباكة', 'تكييف', 'أخرى']
        fault_counts = [12, 8, 15, 5]
        
        ax2.bar(fault_types, fault_counts, color=['#ffc107', '#007bff', '#28a745', '#dc3545'])
        ax2.set_title('الأعطال حسب النوع', fontsize=14, fontweight='bold')
        ax2.set_ylabel('عدد البلاغات')

        plt.tight_layout()

        # إدراج الرسم في النافذة
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_buildings_chart(self, parent):
        """إنشاء رسم بياني للمباني"""
        chart_frame = ttk_bs.Frame(parent, padding=10)
        chart_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الرسم البياني
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # رسم بياني دائري لأنواع المباني
        building_types = ['إداري', 'أكاديمي', 'خدمي', 'تقني']
        building_counts = [3, 2, 2, 1]
        colors = ['#007bff', '#28a745', '#ffc107', '#fd7e14']
        
        ax1.pie(building_counts, labels=building_types, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('توزيع المباني حسب النوع', fontsize=14, fontweight='bold')

        # رسم بياني عمودي لحالة المباني
        building_status = ['نشط', 'قيد الصيانة', 'مغلق مؤقتاً']
        status_counts = [6, 1, 1]
        
        ax2.bar(building_status, status_counts, color=['#28a745', '#ffc107', '#dc3545'])
        ax2.set_title('المباني حسب الحالة', fontsize=14, fontweight='bold')
        ax2.set_ylabel('عدد المباني')

        plt.tight_layout()

        # إدراج الرسم في النافذة
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_detailed_reports(self, parent):
        """إنشاء قسم التقارير المفصلة"""
        reports_frame = ttk_bs.Frame(parent, padding=20)
        reports_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان القسم
        title_label = ttk_bs.Label(
            reports_frame,
            text="📋 التقارير المفصلة",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack(pady=(0, 20))

        # أزرار التقارير
        buttons_frame = ttk_bs.Frame(reports_frame)
        buttons_frame.pack(fill=tk.X, pady=10)

        # الصف الأول من الأزرار
        row1_frame = ttk_bs.Frame(buttons_frame)
        row1_frame.pack(fill=tk.X, pady=5)

        ttk_bs.Button(
            row1_frame,
            text="📊 تقرير شامل للمشاريع",
            command=self.generate_projects_report,
            bootstyle="primary",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        ttk_bs.Button(
            row1_frame,
            text="🔧 تقرير الصيانة الشهري",
            command=self.generate_maintenance_report,
            bootstyle="warning",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        ttk_bs.Button(
            row1_frame,
            text="🏢 تقرير حالة المباني",
            command=self.generate_buildings_report,
            bootstyle="info",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        # الصف الثاني من الأزرار
        row2_frame = ttk_bs.Frame(buttons_frame)
        row2_frame.pack(fill=tk.X, pady=5)

        ttk_bs.Button(
            row2_frame,
            text="💰 التقرير المالي",
            command=self.generate_financial_report,
            bootstyle="success",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        ttk_bs.Button(
            row2_frame,
            text="📈 تقرير الأداء",
            command=self.generate_performance_report,
            bootstyle="secondary",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        ttk_bs.Button(
            row2_frame,
            text="📅 التقرير السنوي",
            command=self.generate_annual_report,
            bootstyle="danger",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        # الصف الثالث - أزرار الطباعة
        row3_frame = ttk_bs.Frame(buttons_frame)
        row3_frame.pack(fill=tk.X, pady=15)

        ttk_bs.Button(
            row3_frame,
            text="🖨️ طباعة التقرير الحالي",
            command=self.print_current_report,
            bootstyle="secondary",
            width=30).pack(side=tk.LEFT, padx=10, ipady=10)

        ttk_bs.Button(
            row3_frame,
            text="💾 حفظ التقرير كملف",
            command=self.save_report_to_file,
            bootstyle="secondary",
            width=30).pack(side=tk.LEFT, padx=10, ipady=10)

        # منطقة معاينة التقرير
        preview_frame = ttk_bs.LabelFrame(
            reports_frame,
            text="👁️ معاينة التقرير",
            padding=15,
            bootstyle="secondary"
        )
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=20)

        self.report_text = tk.Text(
            preview_frame,
            height=15,
            width=80,
            wrap=tk.WORD
        )
        self.report_text.pack(fill=tk.BOTH, expand=True)

        # شريط التمرير للنص
        scrollbar = tk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.report_text.yview)
        self.report_text.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إضافة نص ترحيبي
        welcome_text = """
📋 مرحباً بك في قسم التقارير المفصلة!

يمكنك من هنا إنشاء تقارير مفصلة عن:
• المشاريع وحالتها
• بلاغات الصيانة والأعطال
• حالة المباني والمرافق
• التقارير المالية
• تقارير الأداء
• التقارير السنوية

اختر نوع التقرير المطلوب من الأزرار أعلاه لعرض التفاصيل هنا.
        """
        self.report_text.insert(tk.END, welcome_text)

    def generate_projects_report(self):
        """إنشاء تقرير المشاريع"""
        report = """
📊 تقرير شامل للمشاريع
====================

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

📈 ملخص المشاريع:
• إجمالي المشاريع: 25 مشروع
• المشاريع المكتملة: 8 مشاريع (32%)
• المشاريع قيد التنفيذ: 12 مشروع (48%)
• المشاريع المؤجلة: 3 مشاريع (12%)
• المشاريع الملغية: 2 مشروع (8%)

🏗️ المشاريع حسب النوع:
• مشاريع سكنية: 15 مشروع
• مشاريع تجارية: 8 مشاريع
• مشاريع إدارية: 12 مشروع
• مشاريع صناعية: 5 مشاريع

💰 الميزانية الإجمالية:
• إجمالي الميزانية: 50,000,000 ريال
• المبلغ المنفق: 32,000,000 ريال (64%)
• المبلغ المتبقي: 18,000,000 ريال (36%)

📊 أداء المشاريع:
• متوسط نسبة الإنجاز: 68%
• المشاريع في الموعد: 18 مشروع
• المشاريع المتأخرة: 4 مشاريع
• المشاريع المبكرة: 3 مشاريع

🎯 التوصيات:
• متابعة المشاريع المتأخرة
• تحسين التخطيط الزمني
• زيادة الرقابة على الجودة
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def generate_maintenance_report(self):
        """إنشاء تقرير الصيانة"""
        report = """
🔧 تقرير الصيانة الشهري
=====================

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

📈 ملخص بلاغات الصيانة:
• إجمالي البلاغات: 40 بلاغ
• البلاغات المكتملة: 15 بلاغ (37.5%)
• البلاغات قيد التنفيذ: 8 بلاغات (20%)
• البلاغات الجديدة: 12 بلاغ (30%)
• البلاغات المؤجلة: 5 بلاغات (12.5%)

🔧 الأعطال حسب النوع:
• أعطال كهربائية: 12 بلاغ
• أعطال السباكة: 8 بلاغات
• أعطال التكييف: 15 بلاغ
• أعطال أخرى: 5 بلاغات

⚡ الأولوية:
• أولوية عالية: 10 بلاغات
• أولوية متوسطة: 20 بلاغ
• أولوية منخفضة: 10 بلاغات

🏢 البلاغات حسب المبنى:
• المبنى الإداري: 15 بلاغ
• مبنى الهندسة: 12 بلاغ
• مبنى المختبرات: 8 بلاغات
• مباني أخرى: 5 بلاغات

⏱️ متوسط وقت الاستجابة:
• الأولوية العالية: 2 ساعة
• الأولوية المتوسطة: 8 ساعات
• الأولوية المنخفضة: 24 ساعة

🎯 التوصيات:
• تحسين وقت الاستجابة
• زيادة فريق الصيانة
• الصيانة الوقائية المنتظمة
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def generate_buildings_report(self):
        """إنشاء تقرير المباني"""
        report = """
🏢 تقرير حالة المباني
==================

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

🏗️ ملخص المباني:
• إجمالي المباني: 8 مباني
• المباني النشطة: 6 مباني (75%)
• المباني قيد الصيانة: 1 مبنى (12.5%)
• المباني المغلقة مؤقتاً: 1 مبنى (12.5%)

🏢 المباني حسب النوع:
• مباني إدارية: 3 مباني
• مباني أكاديمية: 2 مبنى
• مباني خدمية: 2 مبنى
• مباني تقنية: 1 مبنى

📐 المساحات:
• إجمالي المساحة: 12,500 م²
• متوسط المساحة: 1,562 م²
• أكبر مبنى: 2,500 م²
• أصغر مبنى: 800 م²

📅 أعمار المباني:
• مباني حديثة (أقل من 5 سنوات): 4 مباني
• مباني متوسطة (5-10 سنوات): 3 مباني
• مباني قديمة (أكثر من 10 سنوات): 1 مبنى

🔧 حالة الصيانة:
• مباني بحالة ممتازة: 5 مباني
• مباني بحالة جيدة: 2 مبنى
• مباني تحتاج صيانة: 1 مبنى

💡 استهلاك الطاقة:
• متوسط الاستهلاك الشهري: 45,000 كيلو واط
• تكلفة الكهرباء الشهرية: 18,000 ريال
• توفير الطاقة المحقق: 15%

🎯 التوصيات:
• تحديث أنظمة التكييف
• تحسين العزل الحراري
• استخدام الطاقة المتجددة
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def generate_financial_report(self):
        """إنشاء التقرير المالي"""
        report = """
💰 التقرير المالي
===============

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

💵 الميزانية العامة:
• إجمالي الميزانية: 75,000,000 ريال
• المبلغ المنفق: 48,500,000 ريال (64.7%)
• المبلغ المتبقي: 26,500,000 ريال (35.3%)

🏗️ مصروفات المشاريع:
• مشاريع البناء: 32,000,000 ريال
• مشاريع التطوير: 8,500,000 ريال
• مشاريع الصيانة: 3,000,000 ريال
• مشاريع أخرى: 5,000,000 ريال

🔧 مصروفات الصيانة:
• الصيانة الطارئة: 1,200,000 ريال
• الصيانة الوقائية: 800,000 ريال
• قطع الغيار: 600,000 ريال
• العمالة: 400,000 ريال

🏢 مصروفات التشغيل:
• الكهرباء: 216,000 ريال/سنوياً
• المياه: 84,000 ريال/سنوياً
• التنظيف: 120,000 ريال/سنوياً
• الأمن: 180,000 ريال/سنوياً

📊 التحليل المالي:
• معدل الإنفاق الشهري: 4,041,667 ريال
• توقع الإنفاق السنوي: 48,500,000 ريال
• نسبة التوفير: 8.5%
• العائد على الاستثمار: 12%

🎯 التوصيات المالية:
• تحسين إدارة التكاليف
• زيادة كفاءة الإنفاق
• البحث عن مصادر تمويل إضافية
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def generate_performance_report(self):
        """إنشاء تقرير الأداء"""
        report = """
📈 تقرير الأداء
=============

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

🎯 مؤشرات الأداء الرئيسية:
• نسبة إنجاز المشاريع: 68%
• نسبة المشاريع في الموعد: 72%
• رضا العملاء: 85%
• كفاءة استخدام الموارد: 78%

⏱️ الأداء الزمني:
• متوسط وقت إنجاز المشاريع: 8.5 شهر
• متوسط التأخير: 15 يوم
• نسبة المشاريع المبكرة: 12%
• نسبة المشاريع المتأخرة: 16%

💰 الأداء المالي:
• نسبة الالتزام بالميزانية: 92%
• متوسط توفير التكاليف: 8.5%
• العائد على الاستثمار: 12%
• كفاءة الإنفاق: 88%

🔧 أداء الصيانة:
• وقت الاستجابة للأعطال: 4 ساعات
• نسبة حل المشاكل من المرة الأولى: 78%
• رضا المستخدمين عن الصيانة: 82%
• تكلفة الصيانة لكل متر مربع: 24 ريال

👥 أداء الفريق:
• إنتاجية الفريق: 85%
• نسبة الحضور: 96%
• معدل دوران الموظفين: 5%
• رضا الموظفين: 88%

🏆 الإنجازات:
• إنجاز 8 مشاريع بنجاح
• توفير 4.2 مليون ريال
• تحسين كفاءة الطاقة بنسبة 15%
• حصول على شهادة الجودة ISO

🎯 مجالات التحسين:
• تقليل وقت إنجاز المشاريع
• تحسين التخطيط المسبق
• زيادة الاستثمار في التدريب
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def generate_annual_report(self):
        """إنشاء التقرير السنوي"""
        report = """
📅 التقرير السنوي 2024
===================

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

🎯 ملخص تنفيذي:
تم خلال عام 2024 إنجاز العديد من المشاريع الهامة وتحقيق نتائج إيجابية
في جميع المجالات. شهد العام نمواً ملحوظاً في الأداء والكفاءة.

📊 الإنجازات الرئيسية:
• إنجاز 25 مشروع بقيمة 50 مليون ريال
• تحسين كفاءة الطاقة بنسبة 15%
• تقليل تكاليف الصيانة بنسبة 12%
• زيادة رضا العملاء إلى 85%

🏗️ المشاريع المنجزة:
• مشروع المبنى الإداري الجديد
• تطوير نظام إدارة المرافق
• تحديث أنظمة التكييف
• مشروع الطاقة الشمسية

💰 الأداء المالي:
• إجمالي الإيرادات: 65 مليون ريال
• إجمالي المصروفات: 48.5 مليون ريال
• صافي الربح: 16.5 مليون ريال
• نسبة الربحية: 25.4%

🔧 تطوير الصيانة:
• تطبيق نظام الصيانة الذكية
• تدريب فرق الصيانة
• تحديث المعدات والأدوات
• تحسين أوقات الاستجابة

🏢 تطوير المرافق:
• إضافة 3 مباني جديدة
• تحديث 5 مباني قائمة
• تحسين البنية التحتية
• تطوير المساحات الخضراء

👥 تطوير الموارد البشرية:
• توظيف 15 موظف جديد
• تدريب 50 موظف
• تحسين بيئة العمل
• زيادة الرواتب بنسبة 8%

🌱 الاستدامة والبيئة:
• تقليل استهلاك الطاقة بنسبة 15%
• تقليل استهلاك المياه بنسبة 10%
• إعادة تدوير 80% من النفايات
• زراعة 200 شجرة

🎯 خطط العام القادم:
• إنجاز 30 مشروع جديد
• زيادة الاستثمار في التقنية
• تطوير الموارد البشرية
• تحسين الاستدامة البيئية

📈 التوقعات:
• نمو الإيرادات بنسبة 20%
• تحسين الكفاءة بنسبة 15%
• زيادة رضا العملاء إلى 90%
• تقليل التكاليف بنسبة 10%
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def print_current_report(self):
        """طباعة التقرير الحالي"""
        try:
            import tempfile
            import os
            
            # الحصول على محتوى التقرير
            report_content = self.report_text.get(1.0, tk.END)
            
            if not report_content.strip():
                messagebox.showwarning("تحذير", "لا يوجد تقرير لطباعته. يرجى إنشاء تقرير أولاً.")
                return
            
            # إنشاء ملف مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(report_content)
                temp_file_path = temp_file.name
            
            # فتح الملف للطباعة
            os.startfile(temp_file_path, "print")
            messagebox.showinfo("نجح", "تم إرسال التقرير للطباعة")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {str(e)}")

    def save_report_to_file(self):
        """حفظ التقرير كملف"""
        try:
            from tkinter import filedialog
            from datetime import datetime
            
            # الحصول على محتوى التقرير
            report_content = self.report_text.get(1.0, tk.END)
            
            if not report_content.strip():
                messagebox.showwarning("تحذير", "لا يوجد تقرير لحفظه. يرجى إنشاء تقرير أولاً.")
                return
            
            # اختيار مكان الحفظ
            current_date = datetime.now().strftime("%Y-%m-%d")
            default_filename = f"تقرير_{current_date}.txt"
            
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                initialvalue=default_filename,
                title="حفظ التقرير"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(report_content)
                messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ التقرير: {str(e)}")