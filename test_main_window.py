#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النافذة الرئيسية
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from engineering_management_system import DatabaseManager, AuthenticationManager

def test_main_window():
    """اختبار النافذة الرئيسية"""
    try:
        # إنشاء مدير قاعدة البيانات والمصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # محاولة تسجيل الدخول
        if auth_manager.authenticate("admin", "admin123"):
            print("تسجيل الدخول نجح!")
            print(f"المستخدم الحالي: {auth_manager.current_user}")
            
            # إنشاء النافذة الرئيسية
            window = ttk_bs.Window(
                title="اختبار النافذة الرئيسية",
                themename="cosmo",
                size=(800, 600)
            )
            
            # إضافة محتوى بسيط
            label = ttk_bs.Label(
                window,
                text=f"مرحباً {auth_manager.current_user['full_name']}!",
                font=("Arial", 16)
            )
            label.pack(pady=50)
            
            button = ttk_bs.Button(
                window,
                text="إغلاق",
                command=window.destroy
            )
            button.pack(pady=20)
            
            # عرض النافذة
            window.mainloop()
            
        else:
            print("فشل في تسجيل الدخول!")
            
    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_main_window()