#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للنظام
"""

import sys
import os

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """اختبار الاستيرادات"""
    try:
        print("🔍 اختبار الاستيرادات...")
        
        import tkinter as tk
        print("✅ tkinter - OK")
        
        import ttkbootstrap as ttk_bs
        print("✅ ttkbootstrap - OK")
        
        from engineering_management_system import DatabaseManager, AuthenticationManager
        print("✅ DatabaseManager, AuthenticationManager - OK")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        print("\n🔍 اختبار قاعدة البيانات...")
        from engineering_management_system import DatabaseManager
        
        db = DatabaseManager()
        print("✅ إنشاء قاعدة البيانات - OK")
        
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        conn.close()
        
        print(f"✅ الجداول الموجودة: {len(tables)}")
        for table in tables:
            print(f"   - {table[0]}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_gui():
    """اختبار الواجهة"""
    try:
        print("\n🔍 اختبار الواجهة...")
        
        import tkinter as tk
        root = tk.Tk()
        root.title("اختبار")
        root.geometry("300x200")
        
        # توسيط النافذة
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        root.geometry(f"{width}x{height}+{x}+{y}")
        
        label = tk.Label(root, text="✅ الواجهة تعمل بنجاح!", font=("Arial", 14))
        label.pack(expand=True)
        
        def close_test():
            print("✅ اختبار الواجهة - OK")
            root.destroy()
        
        button = tk.Button(root, text="إغلاق", command=close_test)
        button.pack(pady=10)
        
        # إغلاق تلقائي بعد 3 ثوان
        root.after(3000, close_test)
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الواجهة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار النظام...")
    print("=" * 50)
    
    # اختبار الاستيرادات
    if not test_imports():
        return
    
    # اختبار قاعدة البيانات
    if not test_database():
        return
    
    # اختبار الواجهة
    if not test_gui():
        return
    
    print("\n" + "=" * 50)
    print("✅ جميع الاختبارات نجحت!")
    print("🚀 يمكنك الآن تشغيل النظام الكامل")
    print("=" * 50)
    
    # تشغيل النظام الكامل
    try:
        print("\n🚀 تشغيل النظام الكامل...")
        from main_gui import MainApplication
        app = MainApplication()
        app.start()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام الكامل: {e}")

if __name__ == "__main__":
    main()