#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جميع مشاكل الخطوط
Fix all font issues
"""

import re
import os

def fix_font_in_file(file_path):
    """إصلاح مشاكل الخطوط في ملف واحد"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # إزالة font من جميع عناصر ttk_bs
        # إزالة font=self.xxx_font
        content = re.sub(r',\s*font=self\.\w+_font', '', content)
        content = re.sub(r'font=self\.\w+_font,\s*', '', content)
        content = re.sub(r'font=self\.\w+_font', '', content)
        
        # إزالة font=(...)
        content = re.sub(r',\s*font=\([^)]+\)', '', content)
        content = re.sub(r'font=\([^)]+\),\s*', '', content)
        content = re.sub(r'font=\([^)]+\)', '', content)
        
        # تنظيف الفواصل المتتالية
        content = re.sub(r',\s*,', ',', content)
        content = re.sub(r'\(\s*,', '(', content)
        content = re.sub(r',\s*\)', ')', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"تم إصلاح الملف: {file_path}")
        else:
            print(f"لا توجد تغييرات في الملف: {file_path}")
        
    except Exception as e:
        print(f"خطأ في إصلاح الملف {file_path}: {str(e)}")

def main():
    """الدالة الرئيسية"""
    files_to_fix = [
        'buildings_management.py',
        'maintenance_management.py', 
        'reports_management.py',
        'simple_project_management.py'
    ]
    
    for file_name in files_to_fix:
        if os.path.exists(file_name):
            fix_font_in_file(file_name)
        else:
            print(f"الملف غير موجود: {file_name}")

if __name__ == "__main__":
    main()