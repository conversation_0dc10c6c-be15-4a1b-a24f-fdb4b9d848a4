import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from datetime import datetime

class BuildingManagementWindow:
    """نافذة إدارة المباني والمرافق"""

    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = None
        self.buildings_tree = None

    def show(self):
        # منع فتح أكثر من نافذة واحدة
        if self.window is not None and self.window.winfo_exists():
            self.window.lift()
            return

        self.window = tk.Toplevel(self.parent)
        self.window.title("🏢 إدارة المباني والمرافق")
        self.window.geometry("1000x600")
        self.window.resizable(True, True)

        self.create_interface()
        self.load_buildings()

        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")

    def create_interface(self):
        frame = ttk_bs.Frame(self.window, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)

        # شريط الأدوات
        toolbar = ttk_bs.Frame(frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        btn_add = ttk_bs.Button(toolbar, text="➕ إضافة مبنى/مرفق", command=self.add_building, bootstyle="success")
        btn_add.pack(side=tk.LEFT, padx=5)

        btn_edit = ttk_bs.Button(toolbar, text="✏️ تعديل", command=self.edit_building, bootstyle="warning")
        btn_edit.pack(side=tk.LEFT, padx=5)

        btn_delete = ttk_bs.Button(toolbar, text="🗑️ حذف", command=self.delete_building, bootstyle="danger")
        btn_delete.pack(side=tk.LEFT, padx=5)

        btn_view = ttk_bs.Button(toolbar, text="📋 عرض التفاصيل", command=self.view_building, bootstyle="info")
        btn_view.pack(side=tk.LEFT, padx=5)

        # شجرة المباني
        columns = ("id", "name", "area", "usage", "construction_year", "owner", "structural_condition")
        self.buildings_tree = ttk_bs.Treeview(frame, columns=columns, show="headings", height=20)

        headers = {
            "id": "🆔 المعرف",
            "name": "اسم المبنى/المرفق",
            "area": "المساحة (م²)",
            "usage": "الاستخدام",
            "construction_year": "سنة الإنشاء",
            "owner": "المالك",
            "structural_condition": "الحالة الإنشائية"
        }

        for col, header in headers.items():
            self.buildings_tree.heading(col, text=header)
            if col == "id":
                self.buildings_tree.column(col, width=50)
            elif col in ["name", "usage", "owner", "structural_condition"]:
                self.buildings_tree.column(col, width=150)
            else:
                self.buildings_tree.column(col, width=100)

        self.buildings_tree.pack(fill=tk.BOTH, expand=True)

    def load_buildings(self):
        if not self.buildings_tree:
            return

        for item in self.buildings_tree.get_children():
            self.buildings_tree.delete(item)

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                SELECT id, name, area, usage, construction_year, owner, structural_condition
                FROM buildings
                ORDER BY name
            ''')
            buildings = cursor.fetchall()
            for b in buildings:
                self.buildings_tree.insert('', 'end', values=b)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المباني: {str(e)}")
        finally:
            conn.close()

    def add_building(self):
        dialog = BuildingDialog(self.window, self.db_manager, title="إضافة مبنى/مرفق")
        if dialog.show():
            self.load_buildings()

    def edit_building(self):
        selected = self.buildings_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مبنى/مرفق للتعديل")
            return
        building_id = self.buildings_tree.item(selected[0])['values'][0]

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('SELECT * FROM buildings WHERE id = ?', (building_id,))
            building_data = cursor.fetchone()
            if not building_data:
                messagebox.showerror("خطأ", "لم يتم العثور على بيانات المبنى")
                return
            columns = [desc[0] for desc in cursor.description]
            building_dict = dict(zip(columns, building_data))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في جلب بيانات المبنى: {str(e)}")
            return
        finally:
            conn.close()

        dialog = BuildingDialog(self.window, self.db_manager, title="تعديل مبنى/مرفق", building_data=building_dict)
        if dialog.show():
            self.load_buildings()

    def delete_building(self):
        selected = self.buildings_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مبنى/مرفق للحذف")
            return
        building_id = self.buildings_tree.item(selected[0])['values'][0]
        building_name = self.buildings_tree.item(selected[0])['values'][1]

        if not messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المبنى/المرفق '{building_name}'؟"):
            return

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('DELETE FROM buildings WHERE id = ?', (building_id,))
            conn.commit()
            messagebox.showinfo("نجح", "تم حذف المبنى/المرفق بنجاح")
            self.load_buildings()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف المبنى/المرفق: {str(e)}")
        finally:
            conn.close()

    def view_building(self):
        selected = self.buildings_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مبنى/مرفق لعرض التفاصيل")
            return
        building_id = self.buildings_tree.item(selected[0])['values'][0]

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('SELECT * FROM buildings WHERE id = ?', (building_id,))
            building_data = cursor.fetchone()
            if not building_data:
                messagebox.showerror("خطأ", "لم يتم العثور على بيانات المبنى")
                return
            columns = [desc[0] for desc in cursor.description]
            building_dict = dict(zip(columns, building_data))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في جلب بيانات المبنى: {str(e)}")
            return
        finally:
            conn.close()

        detail_window = tk.Toplevel(self.window)
        detail_window.title(f"تفاصيل المبنى/المرفق: {building_dict.get('name', '')}")
        detail_window.geometry("500x400")
        detail_window.resizable(False, False)

        frame = ttk_bs.Frame(detail_window, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)

        for key, value in building_dict.items():
            label = ttk_bs.Label(frame, text=f"{key}: {value}", anchor="w")
            label.pack(fill=tk.X, pady=2)


class BuildingDialog:
    """نافذة إضافة/تعديل مبنى/مرفق"""

    def __init__(self, parent, db_manager, title, building_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.title = title
        self.building_data = building_data
        self.result = False
        self.window = None

    def show(self):
        # منع فتح أكثر من نافذة واحدة
        if self.window is not None and self.window.winfo_exists():
            self.window.lift()
            return

        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("600x500")
        self.window.resizable(False, False)
        self.window.transient(self.parent)
        self.window.grab_set()

        self.create_form()

        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")

        self.window.wait_window()
        return self.result

    def create_form(self):
        frame = ttk_bs.Frame(self.window, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)

        # الحقول
        labels = {
            "name": "اسم المبنى/المرفق *",
            "area": "المساحة (م²)",
            "usage": "الاستخدام",
            "construction_year": "سنة الإنشاء",
            "owner": "المالك",
            "structural_condition": "الحالة الإنشائية"
        }

        self.entries = {}

        for key, text in labels.items():
            ttk_bs.Label(frame, text=text).pack(anchor=tk.W, pady=(5, 0))
            if key == "structural_condition":
                combo = ttk_bs.Combobox(frame, values=["جيد", "متوسط", "سيء"], bootstyle="info")
                combo.pack(fill=tk.X, pady=(0, 10))
                self.entries[key] = combo
            else:
                entry = ttk_bs.Entry(frame)
                entry.pack(fill=tk.X, pady=(0, 10))
                self.entries[key] = entry

        # ملء البيانات إذا كان تعديل
        if self.building_data:
            for key in labels.keys():
                value = self.building_data.get(key, "")
                if key == "structural_condition":
                    self.entries[key].set(value)
                else:
                    self.entries[key].insert(0, str(value))

        # أزرار الحفظ والإلغاء
        btn_frame = ttk_bs.Frame(frame)
        btn_frame.pack(fill=tk.X, pady=10)

        btn_save = ttk_bs.Button(btn_frame, text="💾 حفظ", bootstyle="success", command=self.save)
        btn_save.pack(side=tk.RIGHT, padx=5)

        btn_cancel = ttk_bs.Button(btn_frame, text="❌ إلغاء", bootstyle="secondary", command=self.window.destroy)
        btn_cancel.pack(side=tk.RIGHT)

    def save(self):
        # التحقق من صحة البيانات
        name = self.entries["name"].get().strip()
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المبنى/المرفق")
            return

        area = self.entries["area"].get().strip()
        usage = self.entries["usage"].get().strip()
        construction_year = self.entries["construction_year"].get().strip()
        owner = self.entries["owner"].get().strip()
        structural_condition = self.entries["structural_condition"].get().strip()

        # تحقق من صحة سنة الإنشاء إذا تم إدخالها
        if construction_year:
            try:
                year = int(construction_year)
                if year < 1800 or year > datetime.now().year:
                    messagebox.showerror("خطأ", "يرجى إدخال سنة إنشاء صحيحة")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال سنة إنشاء صحيحة")
                return

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        try:
            if self.building_data:  # تعديل
                cursor.execute('''
                    UPDATE buildings
                    SET name=?, area=?, usage=?, construction_year=?, owner=?, structural_condition=?
                    WHERE id=?
                ''', (name, area or None, usage or None, construction_year or None, owner or None, structural_condition or None, self.building_data['id']))
            else:  # إضافة جديد
                cursor.execute('''
                    INSERT INTO buildings (name, area, usage, construction_year, owner, structural_condition)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (name, area or None, usage or None, construction_year or None, owner or None, structural_condition or None))
            conn.commit()
            messagebox.showinfo("نجح", "تم حفظ بيانات المبنى/المرفق بنجاح")
            self.result = True
            self.window.destroy()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
        finally:
            conn.close()
