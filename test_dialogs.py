#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نوافذ الحوار المحسنة
Test Enhanced Dialog Windows
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from engineering_management_system import DatabaseManager, AuthenticationManager

def setup_test_environment():
    """إعداد بيئة الاختبار"""
    # إنشاء قاعدة البيانات ومدير المصادقة
    db_manager = DatabaseManager("data/engineering_system.db")
    auth_manager = AuthenticationManager(db_manager)
    
    # محاكاة تسجيل دخول
    auth_manager.current_user = {
        'id': 1,
        'username': 'admin',
        'full_name': 'مدير النظام',
        'role': 'admin'
    }
    
    return db_manager, auth_manager

def test_project_form():
    """اختبار نموذج المشروع الجديد"""
    try:
        db_manager, auth_manager = setup_test_environment()
        
        # إنشاء نافذة مؤقتة
        temp_root = tk.Tk()
        temp_root.withdraw()
        
        from simple_project_management import ProjectDialog
        dialog = ProjectDialog(temp_root, db_manager, auth_manager, "إضافة مشروع جديد")
        result = dialog.show()
        
        temp_root.destroy()
        
        if result:
            messagebox.showinfo("نجح", "✅ تم حفظ المشروع بنجاح!")
        else:
            messagebox.showinfo("معلومات", "تم إلغاء العملية")
            
    except Exception as e:
        messagebox.showerror("خطأ", f"❌ فشل في اختبار نموذج المشروع:\n{str(e)}")

def test_building_form():
    """اختبار نموذج المبنى الجديد"""
    try:
        db_manager, auth_manager = setup_test_environment()
        
        # إنشاء نافذة مؤقتة
        temp_root = tk.Tk()
        temp_root.withdraw()
        
        from buildings_management import BuildingDialog
        dialog = BuildingDialog(temp_root, db_manager, auth_manager, "إضافة مبنى جديد")
        result = dialog.show()
        
        temp_root.destroy()
        
        if result:
            messagebox.showinfo("نجح", "✅ تم حفظ المبنى بنجاح!")
        else:
            messagebox.showinfo("معلومات", "تم إلغاء العملية")
            
    except Exception as e:
        messagebox.showerror("خطأ", f"❌ فشل في اختبار نموذج المبنى:\n{str(e)}")

def test_maintenance_form():
    """اختبار نموذج بلاغ الصيانة الجديد"""
    try:
        db_manager, auth_manager = setup_test_environment()
        
        # إنشاء نافذة مؤقتة
        temp_root = tk.Tk()
        temp_root.withdraw()
        
        from maintenance_management import MaintenanceRequestDialog
        dialog = MaintenanceRequestDialog(temp_root, db_manager, auth_manager, "إضافة بلاغ صيانة جديد")
        result = dialog.show()
        
        temp_root.destroy()
        
        if result:
            messagebox.showinfo("نجح", "✅ تم حفظ بلاغ الصيانة بنجاح!")
        else:
            messagebox.showinfo("معلومات", "تم إلغاء العملية")
            
    except Exception as e:
        messagebox.showerror("خطأ", f"❌ فشل في اختبار نموذج الصيانة:\n{str(e)}")

def main():
    """الدالة الرئيسية"""
    
    # إنشاء نافذة اختبار
    root = ttk_bs.Window(
        title="🧪 اختبار النوافذ المحسنة",
        themename="cosmo",
        size=(500, 400)
    )
    
    # توسيط النافذة
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    # إطار رئيسي
    main_frame = ttk_bs.Frame(root, padding=30)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # عنوان
    title_label = ttk_bs.Label(
        main_frame,
        text="🧪 اختبار النوافذ المحسنة 🧪",
        bootstyle="primary",
        foreground="#1e3a8a"
    )
    title_label.pack(pady=(0, 20))
    
    # معلومات
    info_label = ttk_bs.Label(
        main_frame,
        text="اختبار النوافذ المحسنة مع:\n• أحجام محسنة\n• بيانات كاملة\n• أزرار حفظ وإلغاء\n• توسيط في الشاشة",
        bootstyle="secondary",
        justify=tk.CENTER
    )
    info_label.pack(pady=(0, 30))
    
    # أزرار الاختبار
    ttk_bs.Button(
        main_frame,
        text="📝 اختبار نموذج المشروع الجديد",
        command=test_project_form,
        bootstyle="primary",
        width=35
    ).pack(pady=10, ipady=10)
    
    ttk_bs.Button(
        main_frame,
        text="🏢 اختبار نموذج المبنى الجديد",
        command=test_building_form,
        bootstyle="info",
        width=35
    ).pack(pady=10, ipady=10)
    
    ttk_bs.Button(
        main_frame,
        text="🔧 اختبار نموذج بلاغ الصيانة",
        command=test_maintenance_form,
        bootstyle="warning",
        width=35
    ).pack(pady=10, ipady=10)
    
    # زر الخروج
    ttk_bs.Button(
        main_frame,
        text="❌ خروج",
        command=root.quit,
        bootstyle="danger",
        width=15
    ).pack(pady=30, ipady=10)
    
    root.mainloop()

if __name__ == "__main__":
    main()