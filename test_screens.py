#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الشاشات الجديدة
Test New Screens
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from engineering_management_system import DatabaseManager, AuthenticationManager

def test_buildings():
    """اختبار شاشة المباني"""
    try:
        # إنشاء نافذة مؤقتة
        temp_root = tk.Tk()
        temp_root.withdraw()  # إخفاء النافذة المؤقتة
        
        # إنشاء قاعدة البيانات ومدير المصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # محاكاة تسجيل دخول
        auth_manager.current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin'
        }
        
        from buildings_management import BuildingsManagementWindow
        buildings_window = BuildingsManagementWindow(temp_root, db_manager, auth_manager)
        buildings_window.show()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في فتح شاشة المباني: {str(e)}")

def test_maintenance():
    """اختبار شاشة الصيانة"""
    try:
        # إنشاء نافذة مؤقتة
        temp_root = tk.Tk()
        temp_root.withdraw()  # إخفاء النافذة المؤقتة
        
        # إنشاء قاعدة البيانات ومدير المصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # محاكاة تسجيل دخول
        auth_manager.current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin'
        }
        
        from maintenance_management import MaintenanceManagementWindow
        maintenance_window = MaintenanceManagementWindow(temp_root, db_manager, auth_manager)
        maintenance_window.show()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في فتح شاشة الصيانة: {str(e)}")

def test_reports():
    """اختبار شاشة التقارير"""
    try:
        # إنشاء نافذة مؤقتة
        temp_root = tk.Tk()
        temp_root.withdraw()  # إخفاء النافذة المؤقتة
        
        # إنشاء قاعدة البيانات ومدير المصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # محاكاة تسجيل دخول
        auth_manager.current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin'
        }
        
        from reports_management import ReportsManagementWindow
        reports_window = ReportsManagementWindow(temp_root, db_manager, auth_manager)
        reports_window.show()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في فتح شاشة التقارير: {str(e)}")

def test_projects():
    """اختبار شاشة المشاريع"""
    try:
        # إنشاء نافذة مؤقتة
        temp_root = tk.Tk()
        temp_root.withdraw()  # إخفاء النافذة المؤقتة
        
        # إنشاء قاعدة البيانات ومدير المصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # محاكاة تسجيل دخول
        auth_manager.current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin'
        }
        
        from simple_project_management import SimpleProjectManagementWindow
        projects_window = SimpleProjectManagementWindow(temp_root, db_manager, auth_manager)
        projects_window.show()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في فتح شاشة المشاريع: {str(e)}")

def main():
    """الدالة الرئيسية"""
    
    # إنشاء نافذة اختبار
    root = ttk_bs.Window(
        title="🧪 اختبار الشاشات الجديدة",
        themename="cosmo",
        size=(500, 350)
    )
    
    # توسيط النافذة
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    # إطار رئيسي
    main_frame = ttk_bs.Frame(root, padding=30)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # عنوان
    title_label = ttk_bs.Label(
        main_frame,
        text="🧪 اختبار الشاشات الجديدة 🧪",
        bootstyle="primary",
        foreground="#1e3a8a"
    )
    title_label.pack(pady=(0, 30))
    
    # إطار الأزرار
    buttons_frame = ttk_bs.Frame(main_frame)
    buttons_frame.pack(fill=tk.X, pady=20)
    
    # الصف الأول
    row1_frame = ttk_bs.Frame(buttons_frame)
    row1_frame.pack(fill=tk.X, pady=10)
    
    ttk_bs.Button(
        row1_frame,
        text="🏗️ اختبار المشاريع",
        command=test_projects,
        bootstyle="primary",
        width=20
    ).pack(side=tk.LEFT, padx=10, ipady=10)
    
    ttk_bs.Button(
        row1_frame,
        text="🏢 اختبار المباني",
        command=test_buildings,
        bootstyle="info",
        width=20
    ).pack(side=tk.LEFT, padx=10, ipady=10)
    
    # الصف الثاني
    row2_frame = ttk_bs.Frame(buttons_frame)
    row2_frame.pack(fill=tk.X, pady=10)
    
    ttk_bs.Button(
        row2_frame,
        text="🔧 اختبار الصيانة",
        command=test_maintenance,
        bootstyle="warning",
        width=20
    ).pack(side=tk.LEFT, padx=10, ipady=10)
    
    ttk_bs.Button(
        row2_frame,
        text="📊 اختبار التقارير",
        command=test_reports,
        bootstyle="success",
        width=20
    ).pack(side=tk.LEFT, padx=10, ipady=10)
    
    # معلومات
    info_label = ttk_bs.Label(
        main_frame,
        text="اختر أحد الأزرار أعلاه لاختبار الشاشات الجديدة",
        bootstyle="secondary"
    )
    info_label.pack(pady=30)
    
    # زر الخروج
    ttk_bs.Button(
        main_frame,
        text="❌ خروج",
        command=root.quit,
        bootstyle="danger",
        width=15
    ).pack(pady=20, ipady=10)
    
    root.mainloop()

if __name__ == "__main__":
    main()