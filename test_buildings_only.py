#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شاشة المباني فقط
Test Buildings Screen Only
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from engineering_management_system import DatabaseManager, AuthenticationManager

def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء نافذة رئيسية
        root = ttk_bs.Window(
            title="🏢 اختبار شاشة المباني",
            themename="cosmo",
            size=(400, 200)
        )
        
        # توسيط النافذة
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        root.geometry(f"{width}x{height}+{x}+{y}")
        
        # إطار رئيسي
        main_frame = ttk_bs.Frame(root, padding=30)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="🏢 اختبار شاشة المباني 🏢",
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))
        
        def open_buildings():
            try:
                # إنشاء قاعدة البيانات ومدير المصادقة
                db_manager = DatabaseManager("data/engineering_system.db")
                auth_manager = AuthenticationManager(db_manager)
                
                # محاكاة تسجيل دخول
                auth_manager.current_user = {
                    'id': 1,
                    'username': 'admin',
                    'full_name': 'مدير النظام',
                    'role': 'admin'
                }
                
                from buildings_management import BuildingsManagementWindow
                buildings_window = BuildingsManagementWindow(root, db_manager, auth_manager)
                buildings_window.show()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح شاشة المباني: {str(e)}")
        
        # زر فتح شاشة المباني
        ttk_bs.Button(
            main_frame,
            text="🏢 فتح شاشة المباني",
            command=open_buildings,
            bootstyle="info",
            width=25
        ).pack(pady=10, ipady=10)
        
        # زر الخروج
        ttk_bs.Button(
            main_frame,
            text="❌ خروج",
            command=root.quit,
            bootstyle="danger",
            width=15
        ).pack(pady=10, ipady=10)
        
        root.mainloop()
        
    except Exception as e:
        print(f"خطأ في التطبيق: {str(e)}")
        messagebox.showerror("خطأ", f"خطأ في التطبيق: {str(e)}")

if __name__ == "__main__":
    main()