#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية لنظام إدارة أعمال الإدارة الهندسية
Main GUI for Engineering Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from engineering_management_system import DatabaseManager, AuthenticationManager
import logging

# استيراد الوحدات المطلوبة
try:
    from simple_project_management import SimpleProjectManagementWindow, SimpleProjectDialog
    PROJECT_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد وحدة المشاريع: {e}")
    PROJECT_MODULE_AVAILABLE = False

try:
    from buildings_management import BuildingsManagementWindow, BuildingDialog
    BUILDINGS_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد وحدة المباني: {e}")
    BUILDINGS_MODULE_AVAILABLE = False

try:
    from maintenance_management import MaintenanceManagementWindow
    MAINTENANCE_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد وحدة الصيانة: {e}")
    MAINTENANCE_MODULE_AVAILABLE = False

try:
    from reports_management import ReportsManagementWindow
    REPORTS_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد وحدة التقارير: {e}")
    REPORTS_MODULE_AVAILABLE = False

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, auth_manager, on_success_callback):
        self.auth_manager = auth_manager
        self.on_success_callback = on_success_callback
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط المحسنة"""
        self.title_font = ("Segoe UI", 18, "bold")
        self.header_font = ("Segoe UI", 16, "bold")
        self.normal_font = ("Segoe UI", 14)
        self.button_font = ("Segoe UI", 12, "bold")

    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.window = ttk_bs.Window(
            title="تسجيل الدخول - نظام إدارة أعمال الإدارة الهندسية",
            themename="cosmo",
            size=(550, 450)
        )

        # توسيط النافذة
        self.center_window()

        # إعداد النافذة
        self.window.resizable(False, False)
        
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان النظام مع تصميم محسن
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 30))

        title_label = ttk_bs.Label(
            title_frame,
            text="🏗️ نظام إدارة أعمال الإدارة الهندسية",
            font=self.title_font,
            bootstyle="primary"
        )
        title_label.pack()

        subtitle_label = ttk_bs.Label(
            title_frame,
            text="Engineering Management System",
            font=("Segoe UI", 10, "italic"),
            bootstyle="secondary"
        )
        subtitle_label.pack(pady=(5, 0))

        # إطار تسجيل الدخول مع تصميم محسن
        login_frame = ttk_bs.LabelFrame(
            main_frame,
            text="تسجيل الدخول",
            padding=20,
            bootstyle="info"
        )
        login_frame.pack(fill=tk.X, pady=10)

        # حقل اسم المستخدم
        ttk_bs.Label(
            login_frame,
            text="👤 اسم المستخدم:",
            font=self.normal_font
        ).pack(anchor=tk.W, pady=(0, 5))

        self.username_entry = ttk_bs.Entry(
            login_frame,
            font=self.normal_font,
            width=35,
            bootstyle="info"
        )
        self.username_entry.pack(pady=(0, 15), ipady=5)
        self.username_entry.insert(0, "admin")  # قيمة افتراضية للاختبار

        # حقل كلمة المرور
        ttk_bs.Label(
            login_frame,
            text="🔒 كلمة المرور:",
            font=self.normal_font
        ).pack(anchor=tk.W, pady=(0, 5))

        self.password_entry = ttk_bs.Entry(
            login_frame,
            font=self.normal_font,
            width=35,
            show="*",
            bootstyle="info"
        )
        self.password_entry.pack(pady=(0, 20), ipady=5)
        self.password_entry.insert(0, "admin123")  # قيمة افتراضية للاختبار

        # أزرار مع تصميم محسن
        button_frame = ttk_bs.Frame(login_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        login_btn = ttk_bs.Button(
            button_frame,
            text="🚀 تسجيل الدخول",
            bootstyle="success",
            command=self.login,
            width=20
        )
        login_btn.pack(side=tk.RIGHT, padx=(5, 0), ipady=8)

        cancel_btn = ttk_bs.Button(
            button_frame,
            text="❌ إلغاء",
            bootstyle="secondary",
            command=self.window.quit,
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT, ipady=8)
        
        # ربط مفتاح Enter بتسجيل الدخول
        self.window.bind('<Return>', lambda e: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
        
        self.window.mainloop()

    def center_window(self):
        """توسيط نافذة تسجيل الدخول"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        if self.auth_manager.authenticate(username, password):
            messagebox.showinfo("نجح", f"مرحباً {self.auth_manager.current_user['full_name']}")
            # إغلاق نافذة تسجيل الدخول وفتح النافذة الرئيسية
            self.window.destroy()
            self.on_success_callback()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")


class MainApplication:
    """التطبيق الرئيسي"""
    
    def __init__(self):
        self.db_manager = DatabaseManager("data/engineering_system.db")
        self.auth_manager = AuthenticationManager(self.db_manager)
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط المحسنة"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.menu_font = ("Segoe UI", 11)
        self.footer_font = ("Segoe UI", 10, "bold")  # خط أزرق غامق وثقيل للتذييل

    def start(self):
        """بدء التطبيق"""
        # عرض نافذة تسجيل الدخول
        login_window = LoginWindow(self.auth_manager, self.show_main_window)
        login_window.show()
    
    def show_main_window(self):
        """عرض النافذة الرئيسية المحسنة"""
        try:
            print("🔄 بدء إنشاء النافذة الرئيسية...")
            
            self.window = ttk_bs.Window(
                title="🏗️ نظام إدارة أعمال الإدارة الهندسية - الإصدار المحسن",
                themename="cosmo",
                size=(1200, 800)
            )
            print("✅ تم إنشاء النافذة بنجاح")

            # توسيط النافذة
            print("🔄 توسيط النافذة...")
            self.center_window(self.window, 1200, 800)
            print("✅ تم توسيط النافذة")
            
            # التأكد من ظهور النافذة في المقدمة
            print("🔄 رفع النافذة للمقدمة...")
            self.window.lift()
            self.window.focus_force()
            print("✅ تم رفع النافذة")
            
            # تطبيق أنماط مخصصة
            print("🔄 تطبيق الأنماط المخصصة...")
            self.apply_custom_styles()
            print("✅ تم تطبيق الأنماط")

            print("🔄 إنشاء القوائم...")
            self.create_menu()
            print("✅ تم إنشاء القوائم")
            
            print("🔄 إنشاء الواجهة الرئيسية...")
            self.create_main_interface()
            print("✅ تم إنشاء الواجهة الرئيسية")
            
            print("🔄 إنشاء التذييل...")
            self.create_footer()
            print("✅ تم إنشاء التذييل")
            
            print("🔄 تحديث التذييل...")
            self.update_footer()
            print("✅ تم تحديث التذييل")

            print("🚀 النافذة الرئيسية جاهزة للعرض...")
            # تم إزالة self.window.mainloop() لتجنب بدء حلقة الأحداث مرتين
            
        except Exception as e:
            print(f"❌ خطأ في عرض النافذة الرئيسية: {e}")
            import traceback
            traceback.print_exc()
            
            # في حالة الخطأ، عرض نافذة بسيطة
            self.show_simple_main_window()

    def show_simple_main_window(self):
        """عرض نافذة رئيسية بسيطة في حالة فشل النافذة المعقدة"""
        print("🔄 عرض النافذة البسيطة...")
        try:
            self.window = ttk_bs.Window(
                title="🏗️ نظام إدارة أعمال الإدارة الهندسية",
                themename="cosmo",
                size=(800, 600)
            )
            
            # توسيط النافذة
            self.center_window(self.window, 800, 600)
            
            # محتوى بسيط
            main_frame = ttk_bs.Frame(self.window, padding=30)
            main_frame.pack(fill=BOTH, expand=True)
            
            # عنوان ترحيبي
            welcome_label = ttk_bs.Label(
                main_frame,
                text=f"🏗️ مرحباً {self.auth_manager.current_user['full_name']} 🏗️",
                font=("Segoe UI", 18, "bold"),
                bootstyle="primary"
            )
            welcome_label.pack(pady=30)
            
            # رسالة
            message_label = ttk_bs.Label(
                main_frame,
                text="تم تسجيل الدخول بنجاح!\nالنافذة الرئيسية تعمل في الوضع البسيط.",
                font=("Segoe UI", 12),
                bootstyle="info",
                justify=CENTER
            )
            message_label.pack(pady=20)
            
            # أزرار
            buttons_frame = ttk_bs.Frame(main_frame)
            buttons_frame.pack(pady=30)
            
            ttk_bs.Button(
                buttons_frame,
                text="🚪 تسجيل الخروج",
                command=self.logout,
                bootstyle="secondary"
            ).pack(side=LEFT, padx=10)
            
            ttk_bs.Button(
                buttons_frame,
                text="❌ إغلاق",
                command=self.window.destroy,
                bootstyle="danger"
            ).pack(side=LEFT, padx=10)
            
            print("✅ النافذة البسيطة جاهزة")
            self.window.mainloop()
            
        except Exception as e:
            print(f"❌ خطأ حتى في النافذة البسيطة: {e}")
            messagebox.showerror("خطأ", f"لا يمكن عرض النافذة الرئيسية: {e}")

    def apply_custom_styles(self):
        """تطبيق الأنماط المخصصة للواجهة"""
        style = ttk_bs.Style()
        
        # تخصيص ألوان الأزرار
        style.configure("Custom.TButton", 
                       foreground="#1e3a8a",
                       borderwidth=2)
        
        # تخصيص ألوان التسميات
        style.configure("Custom.TLabel",
                       foreground="#1e3a8a")
        
        # تخصيص ألوان الإطارات
        style.configure("Custom.TLabelFrame",
                       foreground="#1e3a8a",
                       borderwidth=2)
        
        # تخصيص ألوان الجداول
        style.configure("Custom.Treeview",
                       foreground="#1e3a8a",
                       rowheight=35)
        
        style.configure("Custom.Treeview.Heading",
                       foreground="#1e3a8a",
                       background="#e6f3ff")

    def create_footer(self):
        """إنشاء تذييل الشاشة الرئيسية المحسن"""
        try:
            print("🔄 إنشاء إطار التذييل...")
            # إطار التذييل مع تدرج لوني جميل
            footer_frame = ttk_bs.Frame(self.window)
            footer_frame.pack(fill=tk.X, side=tk.BOTTOM)
            print("✅ تم إنشاء إطار التذييل")

            print("🔄 إنشاء إطار المحتوى...")
            # إطار فرعي للمحتوى مع خلفية ملونة
            content_frame = ttk_bs.Frame(footer_frame)
            content_frame.pack(fill=tk.X, padx=15, pady=12)
            print("✅ تم إنشاء إطار المحتوى")

            print("🔄 إنشاء تسمية المستخدم...")
            # اسم المستخدم (يسار) مع أيقونة
            user_name = self.auth_manager.current_user.get('full_name', 'غير محدد')
            self.user_label = ttk_bs.Label(
                content_frame,
                text=f"👤 المستخدم: {user_name}",
                font=("Segoe UI", 10, "bold"),
                bootstyle="primary"
            )
            self.user_label.pack(side=tk.LEFT)
            print("✅ تم إنشاء تسمية المستخدم")

            print("🔄 إنشاء تسمية البرنامج...")
            # اسم البرنامج مع حقوق النشر (وسط) مع تصميم مميز
            program_label = ttk_bs.Label(
                content_frame,
                text="🏗️ نظام إدارة أعمال الإدارة الهندسية © 2025 🏗️",
                font=("Segoe UI", 10, "bold"),
                bootstyle="primary"
            )
            program_label.pack(expand=True)
            print("✅ تم إنشاء تسمية البرنامج")

            print("🔄 إنشاء تسمية الوقت...")
            # الوقت والتاريخ (يمين) مع أيقونة
            self.time_label = ttk_bs.Label(
                content_frame,
                text="",
                font=("Segoe UI", 10, "bold"),
                bootstyle="primary"
            )
            self.time_label.pack(side=tk.RIGHT)
            print("✅ تم إنشاء تسمية الوقت")

            print("🔄 إنشاء الخط الفاصل...")
            # إضافة خط فاصل علوي للتذييل
            try:
                separator = ttk_bs.Separator(self.window, orient='horizontal')
                separator.pack(fill=tk.X, side=tk.BOTTOM, before=footer_frame)
                print("✅ تم إنشاء الخط الفاصل")
            except Exception as sep_error:
                print(f"⚠️ تحذير: لا يمكن إنشاء الخط الفاصل: {sep_error}")
                # المتابعة بدون الخط الفاصل
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء التذييل: {e}")
            import traceback
            traceback.print_exc()

    def update_footer(self):
        """تحديث معلومات التذييل المحسن"""
        import datetime
        now = datetime.datetime.now()
        
        # تنسيق الوقت والتاريخ بشكل جميل
        date_text = now.strftime("%Y/%m/%d")
        time_text = now.strftime("%H:%M:%S")
        full_text = f"📅 {date_text} ⏰ {time_text}"

        if hasattr(self, 'time_label') and self.time_label.winfo_exists():
            self.time_label.config(text=full_text)

        # تحديث كل ثانية
        if self.window and self.window.winfo_exists():
            self.window.after(1000, self.update_footer)

    def center_window(self, window, width, height):
        """توسيط النافذة في الشاشة"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        window.geometry(f"{width}x{height}+{x}+{y}")

    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="إعدادات النظام", command=self.show_settings)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.window.quit)
        
        # قائمة المشاريع
        projects_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المشاريع", menu=projects_menu)
        projects_menu.add_command(label="إدارة المشاريع", command=self.show_projects)
        projects_menu.add_command(label="مشروع جديد", command=self.new_project)
        
        # قائمة المباني
        buildings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المباني والمرافق", menu=buildings_menu)
        buildings_menu.add_command(label="إدارة المباني", command=self.show_buildings)
        buildings_menu.add_command(label="مبنى جديد", command=self.new_building)
        
        # قائمة الصيانة
        maintenance_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الصيانة", menu=maintenance_menu)
        maintenance_menu.add_command(label="بلاغات الأعطال", command=self.show_maintenance_requests)
        maintenance_menu.add_command(label="الصيانة الوقائية", command=self.show_preventive_maintenance)
        maintenance_menu.add_command(label="سجل الصيانة", command=self.show_maintenance_history)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="لوحة التحكم", command=self.show_dashboard)
        reports_menu.add_command(label="تقارير المشاريع", command=self.show_project_reports)
        reports_menu.add_command(label="تقارير الصيانة", command=self.show_maintenance_reports)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_help)
        help_menu.add_command(label="حول النظام", command=self.show_about)
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية المحسنة"""
        # شريط الحالة العلوي المحسن
        status_frame = ttk_bs.LabelFrame(
            self.window, 
            text="معلومات المستخدم",
            bootstyle="info",
            padding=10
        )
        status_frame.pack(fill=tk.X, padx=15, pady=10)
        
        user_info = f"👤 المستخدم: {self.auth_manager.current_user['full_name']} | 🔐 الصلاحية: {self.auth_manager.current_user['role']}"
        status_label = ttk_bs.Label(
            status_frame, 
            text=user_info, 
            font=self.normal_font,
            foreground="#1e3a8a",
            bootstyle="info"
        )
        status_label.pack(side=tk.LEFT)
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        
        # إنشاء دفتر الملاحظات (Notebook) للتبويبات المحسن
        self.notebook = ttk_bs.Notebook(main_frame, bootstyle="primary")
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تطبيق خط مخصص للتبويبات
        style = ttk_bs.Style()
        style.configure("TNotebook.Tab", 
                       font=self.normal_font,
                       foreground="#1e3a8a",
                       padding=[20, 10])
        
        # تبويب لوحة التحكم
        self.dashboard_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.dashboard_frame, text="🏠 لوحة التحكم")
        
        # تبويب المشاريع
        self.projects_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.projects_frame, text="🏗️ المشاريع")
        
        # تبويب المباني
        self.buildings_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.buildings_frame, text="🏢 المباني والمرافق")
        
        # تبويب الصيانة
        self.maintenance_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.maintenance_frame, text="🔧 الصيانة")
        
        # إنشاء محتوى لوحة التحكم
        self.create_dashboard()
        
        # إنشاء محتوى التبويبات الأخرى
        self.create_projects_tab()
        self.create_buildings_tab()
        self.create_maintenance_tab()
    
    def create_dashboard(self):
        """إنشاء لوحة التحكم المحسنة"""
        # عنوان لوحة التحكم المحسن مع تدرج لوني
        title_frame = ttk_bs.Frame(self.dashboard_frame)
        title_frame.pack(fill=tk.X, pady=20)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🏗️ لوحة التحكم الرئيسية 🏗️",
            font=self.title_font,
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack()
        
        subtitle_label = ttk_bs.Label(
            title_frame,
            text="مرحباً بك في نظام إدارة أعمال الإدارة الهندسية",
            font=self.normal_font,
            bootstyle="secondary"
        )
        subtitle_label.pack(pady=(5, 0))
        
        # إطار الإحصائيات المحسن
        stats_frame = ttk_bs.LabelFrame(
            self.dashboard_frame, 
            text="📊 الإحصائيات السريعة", 
            padding=15,
            bootstyle="info"
        )
        stats_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # إحصائيات في صفوف مع تصميم محسن
        stats_row1 = ttk_bs.Frame(stats_frame)
        stats_row1.pack(fill=tk.X, pady=10)
        
        # بطاقات الإحصائيات المحسنة
        self.create_stat_card(stats_row1, "🏗️ المشاريع النشطة", "0", "success")
        self.create_stat_card(stats_row1, "⚠️ البلاغات المفتوحة", "0", "warning")
        self.create_stat_card(stats_row1, "🏢 المباني المسجلة", "0", "info")
        self.create_stat_card(stats_row1, "🔧 أعمال الصيانة", "0", "secondary")
        
        # إطار الإشعارات المحسن
        notifications_frame = ttk_bs.LabelFrame(
            self.dashboard_frame, 
            text="🔔 الإشعارات والتنبيهات", 
            padding=15,
            bootstyle="warning"
        )
        notifications_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        
        # قائمة الإشعارات المحسنة
        self.notifications_tree = ttk_bs.Treeview(
            notifications_frame,
            columns=("type", "message", "date"),
            show="headings",
            height=10
        )
        
        # تحسين عناوين الأعمدة
        self.notifications_tree.heading("type", text="📋 النوع")
        self.notifications_tree.heading("message", text="💬 الرسالة")
        self.notifications_tree.heading("date", text="📅 التاريخ")
        
        # تحسين عرض الأعمدة
        self.notifications_tree.column("type", width=120)
        self.notifications_tree.column("message", width=500)
        self.notifications_tree.column("date", width=180)
        
        # إضافة شريط تمرير للإشعارات
        notifications_scrollbar = tk.Scrollbar(
            notifications_frame, 
            orient="vertical", 
            command=self.notifications_tree.yview
        )
        self.notifications_tree.configure(yscrollcommand=notifications_scrollbar.set)
        
        self.notifications_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        notifications_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # تحديث الإحصائيات
        self.update_dashboard_stats()
    
    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        # عنوان التبويب
        title_frame = ttk_bs.Frame(self.projects_frame)
        title_frame.pack(fill=tk.X, pady=20)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🏗️ إدارة المشاريع الهندسية 🏗️",
            font=self.title_font,
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack()
        
        # أزرار سريعة للمشاريع
        buttons_frame = ttk_bs.LabelFrame(
            self.projects_frame,
            text="🛠️ أدوات المشاريع السريعة",
            padding=20,
            bootstyle="info"
        )
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # صف الأزرار
        btn_row = ttk_bs.Frame(buttons_frame)
        btn_row.pack(fill=tk.X, pady=10)
        
        ttk_bs.Button(
            btn_row,
            text="📋 عرض جميع المشاريع",
            command=self.show_projects,
            bootstyle="primary",
            width=25
        ).pack(side=tk.LEFT, padx=10, ipady=10)
        
        ttk_bs.Button(
            btn_row,
            text="➕ إضافة مشروع جديد",
            command=self.new_project,
            bootstyle="success",
            width=25
        ).pack(side=tk.LEFT, padx=10, ipady=10)
        
        ttk_bs.Button(
            btn_row,
            text="📊 تقارير المشاريع",
            command=self.show_project_reports,
            bootstyle="warning",
            width=25
        ).pack(side=tk.LEFT, padx=10, ipady=10)
        
        # معلومات إضافية
        info_frame = ttk_bs.LabelFrame(
            self.projects_frame,
            text="📈 معلومات المشاريع",
            padding=20,
            bootstyle="secondary"
        )
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        info_text = """
        🏗️ نظام إدارة المشاريع الهندسية يوفر:
        
        ✅ إدارة شاملة لجميع المشاريع
        ✅ تتبع حالة المشاريع ونسب الإنجاز
        ✅ إدارة المقاولين والتكاليف
        ✅ تقارير مفصلة عن أداء المشاريع
        ✅ واجهة سهلة الاستخدام ومحسنة
        """
        
        info_label = ttk_bs.Label(
            info_frame,
            text=info_text,
            font=self.normal_font,
            foreground="#1e3a8a",
            justify=tk.LEFT
        )
        info_label.pack(anchor=tk.W)
    
    def create_buildings_tab(self):
        """إنشاء تبويب المباني"""
        # عنوان التبويب
        title_frame = ttk_bs.Frame(self.buildings_frame)
        title_frame.pack(fill=tk.X, pady=20)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🏢 إدارة المباني والمرافق 🏢",
            font=self.title_font,
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack()
        
        # رسالة قيد التطوير
        dev_frame = ttk_bs.LabelFrame(
            self.buildings_frame,
            text="🚧 قيد التطوير",
            padding=30,
            bootstyle="warning"
        )
        dev_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        dev_text = """
        🏢 وحدة إدارة المباني والمرافق قيد التطوير
        
        ستتضمن الميزات التالية:
        🏗️ تسجيل وإدارة المباني
        📋 متابعة حالة المرافق
        🔧 جدولة أعمال الصيانة
        📊 تقارير حالة المباني
        """
        
        dev_label = ttk_bs.Label(
            dev_frame,
            text=dev_text,
            font=self.header_font,
            foreground="#1e3a8a",
            justify=tk.CENTER
        )
        dev_label.pack(expand=True)
    
    def create_maintenance_tab(self):
        """إنشاء تبويب الصيانة"""
        # عنوان التبويب
        title_frame = ttk_bs.Frame(self.maintenance_frame)
        title_frame.pack(fill=tk.X, pady=20)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🔧 إدارة أعمال الصيانة 🔧",
            font=self.title_font,
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack()
        
        # رسالة قيد التطوير
        dev_frame = ttk_bs.LabelFrame(
            self.maintenance_frame,
            text="🚧 قيد التطوير",
            padding=30,
            bootstyle="danger"
        )
        dev_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        dev_text = """
        🔧 وحدة إدارة أعمال الصيانة قيد التطوير
        
        ستتضمن الميزات التالية:
        📝 تسجيل بلاغات الأعطال
        🔄 الصيانة الوقائية المجدولة
        📋 سجل أعمال الصيانة
        👷 إدارة فرق الصيانة
        📊 تقارير الصيانة والأعطال
        """
        
        dev_label = ttk_bs.Label(
            dev_frame,
            text=dev_text,
            font=self.header_font,
            foreground="#1e3a8a",
            justify=tk.CENTER
        )
        dev_label.pack(expand=True)
    
    def create_stat_card(self, parent, title, value, style):
        """إنشاء بطاقة إحصائية محسنة"""
        # إطار البطاقة مع تصميم محسن
        card_frame = ttk_bs.LabelFrame(
            parent, 
            text="", 
            padding=15,
            bootstyle=style
        )
        card_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=8, pady=5)
        
        # عنوان البطاقة
        title_label = ttk_bs.Label(
            card_frame, 
            text=title, 
            font=self.normal_font,
            bootstyle=style,
            foreground="#1e3a8a"
        )
        title_label.pack(pady=(0, 8))
        
        # قيمة الإحصائية
        value_label = ttk_bs.Label(
            card_frame, 
            text=value, 
            font=("Segoe UI", 24, "bold"), 
            bootstyle=style,
            foreground="#1e3a8a"
        )
        value_label.pack()
    
    def update_dashboard_stats(self):
        """تحديث إحصائيات لوحة التحكم"""
        # إضافة بعض الإشعارات التوضيحية
        if hasattr(self, 'notifications_tree'):
            # مسح الإشعارات الحالية
            for item in self.notifications_tree.get_children():
                self.notifications_tree.delete(item)
            
            # إضافة إشعارات توضيحية
            sample_notifications = [
                ("معلومات", "مرحباً بك في النظام المحسن! 🎉", "2024/01/15 - 10:30"),
                ("تحديث", "تم تحسين واجهة المستخدم بنجاح ✨", "2024/01/15 - 10:25"),
                ("نجاح", "جميع الشاشات تفتح الآن في وسط الشاشة 🎯", "2024/01/15 - 10:20"),
                ("تحسين", "تم تكبير الخطوط وتحسين الألوان 🎨", "2024/01/15 - 10:15"),
                ("ميزة", "تذييل محسن مع الوقت والتاريخ ⏰", "2024/01/15 - 10:10")
            ]
            
            for notification in sample_notifications:
                self.notifications_tree.insert('', 'end', values=notification)
    
    # وظائف القوائم (ستتم إضافتها لاحقاً)
    def show_settings(self): pass
    def logout(self): 
        self.auth_manager.logout()
        self.window.destroy()
        self.start()
    
    def show_projects(self):
        """عرض نافذة إدارة المشاريع"""
        if PROJECT_MODULE_AVAILABLE:
            projects_window = SimpleProjectManagementWindow(self.window, self.db_manager, self.auth_manager)
            projects_window.show()
        else:
            messagebox.showerror("خطأ", "وحدة إدارة المشاريع غير متوفرة")

    def new_project(self):
        """إضافة مشروع جديد"""
        if PROJECT_MODULE_AVAILABLE:
            dialog = SimpleProjectDialog(self.window, self.db_manager, self.auth_manager, "إضافة مشروع جديد")
            dialog.show()
        else:
            messagebox.showerror("خطأ", "وحدة إدارة المشاريع غير متوفرة")
    def show_buildings(self):
        """عرض نافذة إدارة المباني والمرافق"""
        if BUILDINGS_MODULE_AVAILABLE:
            buildings_window = BuildingsManagementWindow(self.window, self.db_manager, self.auth_manager)
            buildings_window.show()
        else:
            messagebox.showerror("خطأ", "وحدة إدارة المباني غير متوفرة")

    def new_building(self):
        """إضافة مبنى جديد"""
        if BUILDINGS_MODULE_AVAILABLE:
            dialog = BuildingDialog(self.window, self.db_manager, self.auth_manager, "إضافة مبنى جديد")
            dialog.show()
        else:
            messagebox.showerror("خطأ", "وحدة إدارة المباني غير متوفرة")

    def show_maintenance_requests(self):
        """عرض نافذة إدارة الصيانة"""
        if MAINTENANCE_MODULE_AVAILABLE:
            maintenance_window = MaintenanceManagementWindow(self.window, self.db_manager, self.auth_manager)
            maintenance_window.show()
        else:
            messagebox.showerror("خطأ", "وحدة إدارة الصيانة غير متوفرة")

    def show_preventive_maintenance(self):
        """عرض الصيانة الوقائية"""
        if MAINTENANCE_MODULE_AVAILABLE:
            maintenance_window = MaintenanceManagementWindow(self.window, self.db_manager, self.auth_manager)
            maintenance_window.show()
        else:
            messagebox.showerror("خطأ", "وحدة إدارة الصيانة غير متوفرة")

    def show_maintenance_history(self):
        """عرض سجل الصيانة"""
        if MAINTENANCE_MODULE_AVAILABLE:
            maintenance_window = MaintenanceManagementWindow(self.window, self.db_manager, self.auth_manager)
            maintenance_window.show()
        else:
            messagebox.showerror("خطأ", "وحدة إدارة الصيانة غير متوفرة")

    def show_dashboard(self):
        """عرض لوحة التحكم والتقارير"""
        if REPORTS_MODULE_AVAILABLE:
            reports_window = ReportsManagementWindow(self.window, self.db_manager, self.auth_manager)
            reports_window.show()
        else:
            messagebox.showerror("خطأ", "وحدة التقارير غير متوفرة")

    def show_project_reports(self):
        """عرض تقارير المشاريع"""
        if REPORTS_MODULE_AVAILABLE:
            reports_window = ReportsManagementWindow(self.window, self.db_manager, self.auth_manager)
            reports_window.show()
        else:
            messagebox.showerror("خطأ", "وحدة التقارير غير متوفرة")

    def show_maintenance_reports(self):
        """عرض تقارير الصيانة"""
        if REPORTS_MODULE_AVAILABLE:
            reports_window = ReportsManagementWindow(self.window, self.db_manager, self.auth_manager)
            reports_window.show()
        else:
            messagebox.showerror("خطأ", "وحدة التقارير غير متوفرة")
    def show_help(self): pass
    def show_about(self):
        about_text = """
🏗️ نظام إدارة أعمال الإدارة الهندسية - الإصدار المحسن 🏗️

📋 الإصدار: 2.0 Enhanced
🎨 التحسينات الجديدة:
   ✅ جميع الشاشات تفتح في وسط الشاشة
   ✅ خطوط أكبر وأوضح
   ✅ ألوان وأشكال مميزة
   ✅ تذييل محسن مع الوقت والتاريخ
   ✅ خط أزرق غامق وثقيل
   ✅ واجهة مستخدم محسنة بالكامل

👨‍💻 تطوير: فريق التطوير المتخصص
📅 تاريخ التحديث: 2024/01/15
🎯 الهدف: تحسين تجربة المستخدم
        """
        messagebox.showinfo("🏗️ حول النظام المحسن", about_text)

if __name__ == "__main__":
    app = MainApplication()
    app.start()
