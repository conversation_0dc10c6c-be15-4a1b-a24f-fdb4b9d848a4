#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة أعمال الإدارة الهندسية
Engineering Management System

المطور: نظام شامل لإدارة المشاريع والمباني والصيانة
التقنيات: Python + SQLite + Tkinter + ttkbootstrap
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import sqlite3
import os
import json
import datetime
from datetime import date, timedelta
import hashlib
import logging
from pathlib import Path

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path="engineering_system.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('admin', 'engineer', 'technician', 'manager')),
                email TEXT,
                phone TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # جدول المشاريع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                location TEXT,
                project_type TEXT,
                cost REAL,
                contractor TEXT,
                start_date DATE,
                end_date DATE,
                actual_end_date DATE,
                status TEXT DEFAULT 'planning' CHECK (status IN ('planning', 'design', 'tender', 'execution', 'delivery', 'completed')),
                progress_percentage REAL DEFAULT 0,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # جدول مراحل المشروع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS project_phases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER,
                phase_name TEXT NOT NULL,
                start_date DATE,
                end_date DATE,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'delayed')),
                progress_percentage REAL DEFAULT 0,
                notes TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
            )
        ''')
        
        # جدول المباني والمرافق
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS buildings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                location TEXT,
                building_type TEXT,
                area REAL,
                floors INTEGER,
                construction_year INTEGER,
                owner TEXT,
                usage_type TEXT,
                structural_condition TEXT,
                last_inspection_date DATE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الأصول
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS assets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                building_id INTEGER,
                name TEXT NOT NULL,
                asset_type TEXT,
                model TEXT,
                serial_number TEXT,
                purchase_date DATE,
                warranty_end_date DATE,
                condition_status TEXT DEFAULT 'good' CHECK (condition_status IN ('excellent', 'good', 'fair', 'poor', 'out_of_service')),
                location_details TEXT,
                notes TEXT,
                FOREIGN KEY (building_id) REFERENCES buildings (id) ON DELETE CASCADE
            )
        ''')
        
        # جدول بلاغات الأعطال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS maintenance_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                building_id INTEGER,
                asset_id INTEGER,
                title TEXT NOT NULL,
                description TEXT,
                priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
                status TEXT DEFAULT 'open' CHECK (status IN ('open', 'assigned', 'in_progress', 'completed', 'closed')),
                reported_by TEXT,
                assigned_to INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                assigned_at TIMESTAMP,
                completed_at TIMESTAMP,
                notes TEXT,
                FOREIGN KEY (building_id) REFERENCES buildings (id),
                FOREIGN KEY (asset_id) REFERENCES assets (id),
                FOREIGN KEY (assigned_to) REFERENCES users (id)
            )
        ''')
        
        # جدول الصيانة الوقائية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS preventive_maintenance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                asset_id INTEGER,
                maintenance_type TEXT NOT NULL,
                frequency_days INTEGER,
                last_maintenance_date DATE,
                next_maintenance_date DATE,
                assigned_to INTEGER,
                status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'overdue', 'completed')),
                notes TEXT,
                FOREIGN KEY (asset_id) REFERENCES assets (id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_to) REFERENCES users (id)
            )
        ''')
        
        # جدول سجل أعمال الصيانة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS maintenance_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                request_id INTEGER,
                asset_id INTEGER,
                maintenance_type TEXT,
                description TEXT,
                performed_by INTEGER,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                cost REAL,
                parts_used TEXT,
                notes TEXT,
                FOREIGN KEY (request_id) REFERENCES maintenance_requests (id),
                FOREIGN KEY (asset_id) REFERENCES assets (id),
                FOREIGN KEY (performed_by) REFERENCES users (id)
            )
        ''')
        
        # جدول المستندات والملفات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entity_type TEXT NOT NULL CHECK (entity_type IN ('project', 'building', 'asset', 'maintenance')),
                entity_id INTEGER NOT NULL,
                document_name TEXT NOT NULL,
                document_type TEXT,
                file_path TEXT,
                file_size INTEGER,
                uploaded_by INTEGER,
                uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (uploaded_by) REFERENCES users (id)
            )
        ''')
        
        # جدول سجل النشاطات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                entity_type TEXT,
                entity_id INTEGER,
                details TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # إنشاء مستخدم افتراضي
        self.create_default_admin()
    
    def create_default_admin(self):
        """إنشاء مستخدم مدير افتراضي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود مستخدم مدير
        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            # إنشاء كلمة مرور مشفرة
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, role, email)
                VALUES (?, ?, ?, ?, ?)
            ''', ("admin", password_hash, "مدير النظام", "admin", "<EMAIL>"))
            
            conn.commit()
            logging.info("تم إنشاء مستخدم المدير الافتراضي")
        
        conn.close()

class AuthenticationManager:
    """مدير المصادقة والصلاحيات"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.current_user = None
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        password_hash = self.hash_password(password)
        cursor.execute('''
            SELECT * FROM users 
            WHERE username = ? AND password_hash = ? AND is_active = 1
        ''', (username, password_hash))
        
        user = cursor.fetchone()
        
        if user:
            # تحديث آخر تسجيل دخول
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
            ''', (user['id'],))
            conn.commit()
            
            self.current_user = dict(user)
            logging.info(f"تسجيل دخول ناجح للمستخدم: {username}")
            
        conn.close()
        return user is not None
    
    def logout(self):
        """تسجيل الخروج"""
        if self.current_user:
            logging.info(f"تسجيل خروج للمستخدم: {self.current_user['username']}")
            self.current_user = None
    
    def has_permission(self, required_role):
        """التحقق من الصلاحيات"""
        if not self.current_user:
            return False
        
        role_hierarchy = {
            'admin': 4,
            'manager': 3,
            'engineer': 2,
            'technician': 1
        }
        
        user_level = role_hierarchy.get(self.current_user['role'], 0)
        required_level = role_hierarchy.get(required_role, 0)
        
        return user_level >= required_level

# تهيئة النظام
if __name__ == "__main__":
    # إنشاء مجلد البيانات إذا لم يكن موجوداً
    os.makedirs("data", exist_ok=True)
    os.makedirs("documents", exist_ok=True)
    os.makedirs("reports", exist_ok=True)
    
    # تهيئة قاعدة البيانات
    db_manager = DatabaseManager("data/engineering_system.db")
    auth_manager = AuthenticationManager(db_manager)
    
    print("تم تهيئة نظام إدارة أعمال الإدارة الهندسية بنجاح!")
    print("المستخدم الافتراضي: admin")
    print("كلمة المرور الافتراضية: admin123")
