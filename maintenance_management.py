"""
وحدة إدارة الصيانة
Maintenance Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs

class MaintenanceManagementWindow:
    """نافذة إدارة الصيانة"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.window = None
        self.maintenance_tree = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.tree_font = ("Segoe UI", 11)

    def show(self):
        """عرض نافذة إدارة الصيانة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🔧 إدارة الصيانة")
        self.window.geometry("1100x650")
        self.window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        self.create_interface()
        self.load_maintenance_requests()
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة إدارة الصيانة"""
        # عنوان الصفحة
        header_frame = ttk_bs.Frame(self.window)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        title_label = ttk_bs.Label(
            header_frame,
            text="🔧 إدارة الصيانة والأعطال 🔧",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack(side=tk.LEFT)

        # شريط الأدوات
        toolbar_frame = ttk_bs.LabelFrame(
            self.window,
            text="🛠️ أدوات الصيانة",
            padding=15,
            bootstyle="info"
        )
        toolbar_frame.pack(fill=tk.X, padx=15, pady=10)

        # الأزرار
        ttk_bs.Button(
            toolbar_frame,
            text="➕ بلاغ جديد",
            command=self.add_maintenance_request,
            bootstyle="success",
            width=18).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_maintenance_request,
            bootstyle="warning",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="✅ إنجاز",
            command=self.complete_maintenance,
            bootstyle="success",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="📋 تفاصيل",
            command=self.view_maintenance_details,
            bootstyle="info",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🔄 تحديث",
            command=self.load_maintenance_requests,
            bootstyle="secondary",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)
        
        # شريط البحث والفلترة
        search_frame = ttk_bs.LabelFrame(
            self.window,
            text="🔍 البحث والفلترة",
            padding=15,
            bootstyle="secondary"
        )
        search_frame.pack(fill=tk.X, padx=15, pady=10)

        ttk_bs.Label(
            search_frame,
            text="🔍 البحث:"
        ).pack(side=tk.LEFT, padx=(0, 5))

        self.search_entry = ttk_bs.Entry(
            search_frame,
            width=30,
            bootstyle="info"
        )
        self.search_entry.pack(side=tk.LEFT, padx=5, ipady=3)
        self.search_entry.bind('<KeyRelease>', self.filter_maintenance)

        ttk_bs.Label(
            search_frame,
            text="📊 الحالة:"
        ).pack(side=tk.LEFT, padx=(20, 5))

        self.status_filter = ttk_bs.Combobox(
            search_frame,
            width=15,
            bootstyle="warning"
        )
        self.status_filter['values'] = ("الكل", "جديد", "قيد التنفيذ", "مكتمل", "مؤجل")
        self.status_filter.set("الكل")
        self.status_filter.pack(side=tk.LEFT, padx=5, ipady=3)
        self.status_filter.bind('<<ComboboxSelected>>', self.filter_maintenance)

        ttk_bs.Button(
            search_frame,
            text="🔍 بحث",
            command=self.filter_maintenance,
            bootstyle="info",
            width=10
        ).pack(side=tk.LEFT, padx=5, ipady=3)
        
        # جدول بلاغات الصيانة
        tree_frame = ttk_bs.LabelFrame(
            self.window,
            text="🔧 بلاغات الصيانة والأعطال",
            padding=15,
            bootstyle="primary"
        )
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # إنشاء Treeview
        columns = ("id", "title", "building_id", "priority", "status", "reported_by", "assigned_to", "created_at", "notes")
        self.maintenance_tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تطبيق خط على الجدول
        style = ttk.Style()
        style.configure("Treeview", rowheight=30)
        style.configure("Treeview.Heading", foreground="#1e3a8a")
        
        # تعريف العناوين
        headers = {
            "id": "🆔 المعرف",
            "title": "📝 عنوان البلاغ",
            "building_id": "🏢 معرف المبنى",
            "priority": "⚡ الأولوية",
            "status": "📊 الحالة",
            "reported_by": "👤 المبلغ",
            "assigned_to": "👷 المكلف",
            "created_at": "📅 تاريخ الإنشاء",
            "notes": "📝 ملاحظات"
        }
        
        for col, header in headers.items():
            self.maintenance_tree.heading(col, text=header)
            if col == "id":
                self.maintenance_tree.column(col, width=50)
            elif col in ["title", "building_id"]:
                self.maintenance_tree.column(col, width=150)
            else:
                self.maintenance_tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar_y = tk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.maintenance_tree.yview)
        scrollbar_x = tk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.maintenance_tree.xview)
        self.maintenance_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تعبئة الجدول وشريط التمرير
        self.maintenance_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # ربط النقر المزدوج
        self.maintenance_tree.bind('<Double-1>', lambda e: self.view_maintenance_details())
    
    def load_maintenance_requests(self):
        """تحميل بلاغات الصيانة من قاعدة البيانات"""
        if not self.maintenance_tree:
            return

        # مسح البيانات الحالية
        for item in self.maintenance_tree.get_children():
            self.maintenance_tree.delete(item)

        try:
            # قراءة البيانات من قاعدة البيانات
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, title, building_id, priority, status, reported_by,
                       assigned_to, created_at, notes
                FROM maintenance_requests
                ORDER BY created_at DESC
            ''')

            requests = cursor.fetchall()

            # خرائط التحويل من الإنجليزية إلى العربية للعرض
            priority_en_to_ar = {
                "urgent": "عالية",
                "high": "عالية",
                "medium": "متوسطة",
                "low": "منخفضة"
            }

            status_en_to_ar = {
                "open": "جديد",
                "assigned": "مؤجل",
                "in_progress": "قيد التنفيذ",
                "completed": "مكتمل",
                "closed": "مغلق"
            }

            for request in requests:
                # تحويل القيم للعرض
                display_request = list(request)

                # تحويل الأولوية
                if request[3]:  # priority
                    display_request[3] = priority_en_to_ar.get(request[3], request[3])

                # تحويل الحالة
                if request[4]:  # status
                    display_request[4] = status_en_to_ar.get(request[4], request[4])

                # تنسيق التاريخ
                if request[7]:  # created_at
                    try:
                        from datetime import datetime
                        if isinstance(request[7], str):
                            dt = datetime.fromisoformat(request[7].replace('Z', '+00:00'))
                            display_request[7] = dt.strftime('%Y/%m/%d %H:%M')
                        else:
                            display_request[7] = str(request[7])
                    except:
                        display_request[7] = str(request[7])

                # إضافة البيانات للجدول
                self.maintenance_tree.insert('', 'end', values=display_request)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بلاغات الصيانة: {str(e)}")
            # في حالة الخطأ، عرض بيانات تجريبية
            sample_requests = [
                (1, "عطل في نظام التكييف", 101, "عالية", "قيد التنفيذ", "أحمد محمد", "فريق الصيانة", "2024/01/15", ""),
                (2, "تسريب في السباكة", 102, "متوسطة", "جديد", "سارة أحمد", "غير محدد", "2024/01/14", "")
            ]
            for request in sample_requests:
                self.maintenance_tree.insert('', 'end', values=request)
    
    def filter_maintenance(self, event=None):
        """فلترة بلاغات الصيانة"""
        search_text = self.search_entry.get().lower()
        status_filter = self.status_filter.get()
        
        # مسح البيانات الحالية
        for item in self.maintenance_tree.get_children():
            self.maintenance_tree.delete(item)
        
        # إعادة تحميل البيانات مع الفلترة
        self.load_maintenance_requests()

        # تطبيق الفلترة على البيانات المحملة
        if search_text or status_filter != "الكل":
            # جمع جميع العناصر
            all_items = []
            for item in self.maintenance_tree.get_children():
                values = self.maintenance_tree.item(item)['values']
                all_items.append((item, values))

            # مسح الجدول
            for item in self.maintenance_tree.get_children():
                self.maintenance_tree.delete(item)

            # إعادة إدراج العناصر المطابقة للفلتر
            for item, values in all_items:
                # فلترة حسب النص
                text_match = (not search_text or
                             search_text in str(values[1]).lower() or
                             search_text in str(values[5]).lower() or
                             search_text in str(values[8]).lower())

                # فلترة حسب الحالة
                status_match = (status_filter == "الكل" or status_filter == values[4])

                if text_match and status_match:
                    self.maintenance_tree.insert('', 'end', values=values)
    
    def add_maintenance_request(self):
        """إضافة بلاغ صيانة جديد"""
        dialog = MaintenanceDialog(self.window, self.db_manager, self.auth_manager, "إضافة بلاغ صيانة جديد")
        if dialog.show():
            self.load_maintenance_requests()
    
    def edit_maintenance_request(self):
        """تعديل بلاغ صيانة"""
        selected = self.maintenance_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بلاغ للتعديل")
            return
        
        request_data = self.maintenance_tree.item(selected[0])['values']
        dialog = MaintenanceDialog(self.window, self.db_manager, self.auth_manager, "تعديل بلاغ الصيانة", request_data)
        if dialog.show():
            self.load_maintenance_requests()
    
    def complete_maintenance(self):
        """إنجاز بلاغ الصيانة"""
        selected = self.maintenance_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بلاغ لإنجازه")
            return
        
        request_title = self.maintenance_tree.item(selected[0])['values'][1]
        
        if messagebox.askyesno("تأكيد الإنجاز", f"هل تم إنجاز '{request_title}' بنجاح؟"):
            messagebox.showinfo("نجح", "تم تحديث حالة البلاغ إلى 'مكتمل'")
            self.load_maintenance_requests()
    
    def view_maintenance_details(self):
        """عرض تفاصيل بلاغ الصيانة"""
        selected = self.maintenance_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بلاغ لعرض التفاصيل")
            return
        
        request_data = self.maintenance_tree.item(selected[0])['values']
        details = f"""
🔧 تفاصيل بلاغ الصيانة:

🆔 المعرف: {request_data[0]}
📝 العنوان: {request_data[1]}
🏢 المبنى: {request_data[2]}
🔧 نوع العطل: {request_data[3]}
⚡ الأولوية: {request_data[4]}
📊 الحالة: {request_data[5]}
👤 المبلغ: {request_data[6]}
📅 التاريخ: {request_data[7]}
👷 المكلف: {request_data[8]}
        """
        messagebox.showinfo("تفاصيل بلاغ الصيانة", details)

class MaintenanceDialog:
    """نافذة إضافة/تعديل بلاغ صيانة"""
    
    def __init__(self, parent, db_manager, auth_manager, title, request_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.title = title
        self.request_data = request_data
        self.result = False

        # خرائط التحويل بين العربية والإنجليزية
        self.priority_ar_to_en = {
            "عالية": "urgent",
            "متوسطة": "medium",
            "منخفضة": "low"
        }
        self.priority_en_to_ar = {v: k for k, v in self.priority_ar_to_en.items()}

        self.status_ar_to_en = {
            "جديد": "open",
            "قيد التنفيذ": "in_progress",
            "مكتمل": "completed",
            "مؤجل": "assigned"
        }
        self.status_en_to_ar = {v: k for k, v in self.status_ar_to_en.items()}
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.label_font = ("Segoe UI", 11)

    def show(self):
        """عرض النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("950x600")
        self.window.resizable(False, False)
        self.window.transient(self.parent)
        self.window.grab_set()

        self.create_form()

        # تفعيل زر الحفظ عند عرض النافذة
        if hasattr(self, 'save_button'):
            self.save_button.config(state="normal")

        # توسيط النافذة
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

        self.window.wait_window()
        return self.result

    def create_form(self):
        """إنشاء نموذج بلاغ الصيانة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=25)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النموذج
        title_label = ttk_bs.Label(
            main_frame,
            text=f"🔧 {self.title} 🔧",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 20), sticky="w")

        # الحقول في صفين وعمودين
        labels = [
            ("📝 عنوان البلاغ: *", "title_entry", "info"),
            ("📄 وصف المشكلة:", "description_text", None),
            ("🏢 المبنى:", "building_combo", "info"),
            ("🔧 نوع العطل:", "type_combo", "warning"),
            ("⚡ الأولوية:", "priority_combo", "danger"),
            ("📊 الحالة:", "status_combo", "success"),
            ("👷 المكلف بالصيانة:", "assigned_entry", "secondary"),
            ("📅 تاريخ البلاغ:", "date_entry", "info"),
            ("💰 التكلفة المقدرة:", "cost_entry", "warning"),
            ("📝 ملاحظات إضافية:", "notes_text", None)
        ]

        self.entries = {}

        for idx, (label_text, attr_name, style) in enumerate(labels):
            row = (idx // 2) + 1
            col = (idx % 2) * 2
            ttk_bs.Label(main_frame, text=label_text).grid(row=row, column=col, sticky="w", pady=(0, 5), padx=(0, 10))
            if attr_name in ["description_text", "notes_text"]:
                text = tk.Text(main_frame, height=4 if attr_name == "description_text" else 3, width=45, wrap=tk.WORD)
                text.grid(row=row, column=col+1, pady=(0, 15), sticky="w")
                self.entries[attr_name] = text
            elif attr_name.endswith("_combo"):
                combo = ttk_bs.Combobox(main_frame, width=45, bootstyle=style)
                if attr_name == "building_combo":
                    combo['values'] = ("1 - المبنى الإداري", "2 - مبنى الهندسة", "3 - مبنى المختبرات", "4 - مبنى الصيانة", "5 - مبنى المؤتمرات")
                elif attr_name == "type_combo":
                    combo['values'] = ("تكييف", "سباكة", "كهرباء", "مصاعد", "تنظيف", "تقنية", "نجارة", "أخرى")
                elif attr_name == "priority_combo":
                    combo['values'] = ("عالية", "متوسطة", "منخفضة")
                elif attr_name == "status_combo":
                    combo['values'] = ("جديد", "قيد التنفيذ", "مكتمل", "مؤجل")
                combo.grid(row=row, column=col+1, pady=(0, 15), sticky="w")
                self.entries[attr_name] = combo
            elif attr_name == "date_entry":
                # استخدام منتقي التاريخ
                from datetime import datetime
                date_entry = ttk_bs.DateEntry(main_frame, dateformat="%Y-%m-%d", width=43, bootstyle=style)
                date_entry.grid(row=row, column=col+1, pady=(0, 15), sticky="w")
                self.entries[attr_name] = date_entry
            else:
                entry = ttk_bs.Entry(main_frame, width=45, bootstyle=style)
                entry.grid(row=row, column=col+1, pady=(0, 15), sticky="w")
                self.entries[attr_name] = entry

        # ملء البيانات إذا كان تعديل
        if self.request_data:
            self.entries["title_entry"].insert(0, self.request_data[1])
            # وصف المشكلة - إضافة وصف افتراضي إذا لم يكن موجود
            description = f"وصف المشكلة: {self.request_data[1]}" if len(self.request_data) <= 2 else (self.request_data[2] or "")
            self.entries["description_text"].insert('1.0', description)
            # المبنى
            building_id = str(self.request_data[2]) if len(self.request_data) > 2 else "غير محدد"
            self.entries["building_combo"].set(f"مبنى رقم {building_id}")
            # نوع العطل - استخدام الأولوية كنوع مؤقت
            self.entries["type_combo"].set("تكييف" if self.request_data[3] == "عالية" else "عام")
            # الأولوية - تحويل من الإنجليزية إلى العربية
            priority_ar = self.priority_en_to_ar.get(self.request_data[3], "متوسطة")
            self.entries["priority_combo"].set(priority_ar)
            # الحالة - تحويل من الإنجليزية إلى العربية
            status_ar = self.status_en_to_ar.get(self.request_data[4], "جديد")
            self.entries["status_combo"].set(status_ar)
            # المكلف
            self.entries["assigned_entry"].insert(0, self.request_data[6] if len(self.request_data) > 6 else "")
            # التاريخ
            if len(self.request_data) > 7 and self.request_data[7]:
                try:
                    # تحويل التاريخ إلى تنسيق مناسب لمنتقي التاريخ
                    date_str = str(self.request_data[7])
                    if '/' in date_str:
                        # تحويل من تنسيق YYYY/MM/DD إلى YYYY-MM-DD
                        date_str = date_str.split(' ')[0].replace('/', '-')
                    elif ' ' in date_str:
                        # أخذ الجزء الأول فقط (التاريخ بدون الوقت)
                        date_str = date_str.split(' ')[0]

                    self.entries["date_entry"].entry.delete(0, tk.END)
                    self.entries["date_entry"].entry.insert(0, date_str)
                except:
                    # في حالة الخطأ، استخدام التاريخ الحالي
                    from datetime import datetime
                    today = datetime.now().strftime("%Y-%m-%d")
                    self.entries["date_entry"].entry.delete(0, tk.END)
                    self.entries["date_entry"].entry.insert(0, today)
            # التكلفة - قيمة افتراضية
            self.entries["cost_entry"].insert(0, "0")
            # الملاحظات
            notes = self.request_data[8] if len(self.request_data) > 8 else ""
            self.entries["notes_text"].insert('1.0', notes)

        # الأزرار
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.grid(row=row+1, column=0, columnspan=4, pady=20, sticky="e")

        self.save_button = ttk_bs.Button(
            button_frame,
            text="💾 حفظ",
            command=self.save_request,
            bootstyle="success",
            width=18)
        self.save_button.pack(side=tk.RIGHT, padx=(15, 0), ipady=10)

        self.cancel_button = ttk_bs.Button(
            button_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bootstyle="secondary",
            width=18)
        self.cancel_button.pack(side=tk.RIGHT, ipady=10)
    
    def save_request(self):
        """حفظ بيانات بلاغ الصيانة"""
        try:
            title = self.entries["title_entry"].get().strip()
            description = self.entries["description_text"].get('1.0', tk.END).strip()
            building = self.entries["building_combo"].get().strip()
            priority = self.entries["priority_combo"].get().strip()
            status = self.entries["status_combo"].get().strip()
            assigned_to = self.entries["assigned_entry"].get().strip()
            notes = self.entries["notes_text"].get('1.0', tk.END).strip()

            # قراءة التاريخ من منتقي التاريخ
            try:
                selected_date = self.entries["date_entry"].entry.get().strip()
                if not selected_date:
                    # استخدام التاريخ الحالي إذا لم يتم اختيار تاريخ
                    from datetime import datetime
                    selected_date = datetime.now().strftime("%Y-%m-%d")
            except:
                # في حالة الخطأ، استخدام التاريخ الحالي
                from datetime import datetime
                selected_date = datetime.now().strftime("%Y-%m-%d")
        except KeyError as e:
            messagebox.showerror("خطأ", f"خطأ في الوصول إلى الحقل: {e}")
            return
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة البيانات: {e}")
            return

        if not title:
            messagebox.showerror("خطأ", "يرجى إدخال عنوان البلاغ")
            return

        # تحويل معرف المبنى إلى رقم أو NULL
        building_id = None
        if building and building != "غير محدد":
            try:
                # استخراج الرقم من النص مثل "1 - المبنى الإداري"
                if " - " in building:
                    building_id = int(building.split(" - ")[0])
                else:
                    # محاولة استخراج أي رقم من النص
                    import re
                    match = re.search(r'\d+', building)
                    if match:
                        building_id = int(match.group())
                    else:
                        building_id = 1  # قيمة افتراضية
            except:
                building_id = 1  # قيمة افتراضية في حالة الخطأ

        # تحويل assigned_to إلى رقم أو NULL
        assigned_to_id = None
        if assigned_to and assigned_to.strip():
            try:
                assigned_to_id = int(assigned_to) if assigned_to.isdigit() else None
            except:
                assigned_to_id = None

        # تحويل الأولوية والحالة من العربية إلى الإنجليزية
        priority_en = self.priority_ar_to_en.get(priority, "medium")
        status_en = self.status_ar_to_en.get(status, "open")

        # حفظ في قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if self.request_data:  # تعديل
                cursor.execute('''
                    UPDATE maintenance_requests
                    SET title=?, description=?, building_id=?, priority=?, status=?,
                        assigned_to=?, notes=?
                    WHERE id=?
                ''', (title, description, building_id, priority_en, status_en,
                      assigned_to_id, notes, self.request_data[0]))
            else:  # إضافة جديد
                cursor.execute('''
                    INSERT INTO maintenance_requests (title, description, building_id, priority,
                                                    status, reported_by, assigned_to, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (title, description, building_id, priority_en, status_en,
                      self.auth_manager.current_user.get('username', 'مجهول'), assigned_to_id, notes))
            
            conn.commit()
            messagebox.showinfo("نجح", "تم حفظ بلاغ الصيانة بنجاح")
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
        finally:
            conn.close()
