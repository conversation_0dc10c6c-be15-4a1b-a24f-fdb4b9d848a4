-- SQL script to add missing columns to buildings table for compatibility with buildings_management.py

ALTER TABLE buildings ADD COLUMN type TEXT;
ALTER TABLE buildings ADD COLUMN location TEXT;
ALTER TABLE buildings ADD COLUMN floors INTEGER;
ALTER TABLE buildings ADD COLUMN status TEXT;
ALTER TABLE buildings ADD COLUMN contractor TEXT;
ALTER TABLE buildings ADD COLUMN cost REAL;
ALTER TABLE buildings ADD COLUMN notes TEXT;
ALTER TABLE buildings ADD COLUMN created_by INTEGER;
ALTER TABLE buildings ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Note: Before running this script, please backup your database.
