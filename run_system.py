#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة أعمال الإدارة الهندسية
Engineering Management System Launcher
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        messagebox.showerror(
            "خطأ في الإصدار",
            "يتطلب النظام Python 3.8 أو أحدث\n"
            f"الإصدار الحالي: {sys.version}"
        )
        return False
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    try:
        print("جاري تثبيت المتطلبات...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("تم تثبيت المتطلبات بنجاح!")
        return True
    except subprocess.CalledProcessError as e:
        messagebox.showerror(
            "خطأ في التثبيت",
            f"فشل في تثبيت المتطلبات:\n{str(e)}"
        )
        return False
    except FileNotFoundError:
        messagebox.showerror(
            "خطأ",
            "ملف requirements.txt غير موجود"
        )
        return False

def check_requirements():
    """التحقق من وجود المتطلبات"""
    required_packages = [
        'ttkbootstrap',
        'reportlab',
        'xlsxwriter',
        'PIL',
        'matplotlib',
        'pandas',
        'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                __import__('PIL')
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        response = messagebox.askyesno(
            "مكتبات مفقودة",
            f"المكتبات التالية مفقودة:\n{', '.join(missing_packages)}\n\n"
            "هل تريد تثبيتها الآن؟"
        )
        
        if response:
            return install_requirements()
        else:
            return False
    
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['data', 'documents', 'reports', 'backups', 'logs']
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("تم إنشاء المجلدات المطلوبة")

def run_system():
    """تشغيل النظام"""
    try:
        from main_gui import MainApplication
        
        print("بدء تشغيل نظام إدارة أعمال الإدارة الهندسية...")
        app = MainApplication()
        app.start()
        
    except ImportError as e:
        messagebox.showerror(
            "خطأ في الاستيراد",
            f"فشل في استيراد النظام:\n{str(e)}"
        )
    except Exception as e:
        messagebox.showerror(
            "خطأ في التشغيل",
            f"حدث خطأ أثناء تشغيل النظام:\n{str(e)}"
        )

def main():
    """الوظيفة الرئيسية"""
    print("=" * 50)
    print("نظام إدارة أعمال الإدارة الهندسية")
    print("Engineering Management System")
    print("=" * 50)
    
    # التحقق من إصدار Python
    if not check_python_version():
        return
    
    # إنشاء المجلدات
    create_directories()
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("فشل في التحقق من المتطلبات")
        return
    
    # تشغيل النظام
    run_system()

if __name__ == "__main__":
    main()
