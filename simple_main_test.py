#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للنافذة الرئيسية مع التذييل المحسن
Simple Test for Main Window with Enhanced Footer
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from engineering_management_system import DatabaseManager, AuthenticationManager
import datetime

class SimpleMainApp:
    """تطبيق رئيسي بسيط مع التذييل المحسن"""
    
    def __init__(self):
        # إعداد قاعدة البيانات والمصادقة
        self.db_manager = DatabaseManager("data/engineering_system.db")
        self.auth_manager = AuthenticationManager(self.db_manager)
        
        # تسجيل دخول تلقائي للاختبار
        self.auth_manager.authenticate("admin", "admin123")
        
        self.setup_fonts()
        self.create_main_window()
        
    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.footer_font = ("Segoe UI", 10, "bold")
        
    def create_main_window(self):
        """إنشاء النافذة الرئيسية"""
        print("🔍 إنشاء النافذة الرئيسية...")
        
        self.window = ttk_bs.Window(
            title="🏗️ نظام إدارة أعمال الإدارة الهندسية - اختبار التذييل",
            themename="cosmo",
            size=(1000, 700)
        )
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        
        # إنشاء التذييل المحسن
        self.create_footer()
        
        # بدء تحديث التذييل
        self.update_footer()
        
        print("✅ النافذة الرئيسية جاهزة!")
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 1000
        height = 700
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # عنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="🧪 اختبار التذييل المحسن",
            font=self.title_font,
            bootstyle="primary"
        )
        title_label.pack(pady=20)
        
        # معلومات المستخدم
        user_info_frame = ttk_bs.LabelFrame(
            main_frame,
            text="معلومات المستخدم",
            padding=15,
            bootstyle="info"
        )
        user_info_frame.pack(fill=tk.X, pady=10)
        
        user_name = self.auth_manager.current_user.get('full_name', 'غير محدد')
        user_role = self.auth_manager.current_user.get('role', 'غير محدد')
        
        user_info_text = f"👤 المستخدم: {user_name}\n🔐 الصلاحية: {user_role}"
        user_info_label = ttk_bs.Label(
            user_info_frame,
            text=user_info_text,
            font=self.normal_font,
            foreground="#1e3a8a"
        )
        user_info_label.pack()
        
        # معلومات التذييل
        footer_info_frame = ttk_bs.LabelFrame(
            main_frame,
            text="🎯 مواصفات التذييل المحسن",
            padding=15,
            bootstyle="success"
        )
        footer_info_frame.pack(fill=tk.X, pady=10)
        
        footer_info_text = """✅ اسم المستخدم في اليسار
✅ اسم البرنامج مع حقوق النشر © 2025 في الوسط
✅ التاريخ والوقت في اليمين
✅ خط أزرق غامق وثقيل قليلاً
✅ تحديث تلقائي للوقت كل ثانية"""
        
        footer_info_label = ttk_bs.Label(
            footer_info_frame,
            text=footer_info_text,
            font=self.normal_font,
            foreground="#1e3a8a",
            justify=tk.LEFT
        )
        footer_info_label.pack(anchor=tk.W)
        
        # زر الخروج
        exit_btn = ttk_bs.Button(
            main_frame,
            text="❌ خروج",
            command=self.window.quit,
            bootstyle="danger",
            width=20
        )
        exit_btn.pack(pady=20)
        
    def create_footer(self):
        """إنشاء تذييل الشاشة الرئيسية المحسن"""
        print("🔍 إنشاء التذييل...")
        
        # إطار التذييل
        footer_frame = ttk_bs.Frame(self.window)
        footer_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # إطار فرعي للمحتوى
        content_frame = ttk_bs.Frame(footer_frame)
        content_frame.pack(fill=tk.X, padx=15, pady=12)
        
        # اسم المستخدم (يسار) مع أيقونة
        user_name = self.auth_manager.current_user.get('full_name', 'غير محدد')
        self.user_label = ttk_bs.Label(
            content_frame,
            text=f"👤 المستخدم: {user_name}",
            font=("Segoe UI", 10, "bold"),  # خط ثقيل قليلاً
            foreground="#1e3a8a",  # أزرق غامق
            bootstyle="primary"
        )
        self.user_label.pack(side=tk.LEFT)
        
        # اسم البرنامج مع حقوق النشر (وسط) مع تصميم مميز
        program_label = ttk_bs.Label(
            content_frame,
            text="🏗️ نظام إدارة أعمال الإدارة الهندسية © 2025 🏗️",
            font=("Segoe UI", 10, "bold"),  # خط ثقيل قليلاً
            foreground="#1e3a8a",  # أزرق غامق
            bootstyle="primary"
        )
        program_label.pack(expand=True)
        
        # الوقت والتاريخ (يمين) مع أيقونة
        self.time_label = ttk_bs.Label(
            content_frame,
            text="",
            font=("Segoe UI", 10, "bold"),  # خط ثقيل قليلاً
            foreground="#1e3a8a",  # أزرق غامق
            bootstyle="primary"
        )
        self.time_label.pack(side=tk.RIGHT)
        
        # إضافة خط فاصل علوي للتذييل
        separator = ttk_bs.Separator(self.window, orient='horizontal')
        separator.pack(fill=tk.X, side=tk.BOTTOM, before=footer_frame)
        
        print("✅ التذييل تم إنشاؤه بنجاح!")
        
    def update_footer(self):
        """تحديث معلومات التذييل المحسن"""
        now = datetime.datetime.now()
        
        # تنسيق الوقت والتاريخ بشكل جميل
        date_text = now.strftime("%Y/%m/%d")
        time_text = now.strftime("%H:%M:%S")
        full_text = f"📅 {date_text} ⏰ {time_text}"
        
        if hasattr(self, 'time_label') and self.time_label.winfo_exists():
            self.time_label.config(text=full_text)
        
        # تحديث كل ثانية
        if self.window.winfo_exists():
            self.window.after(1000, self.update_footer)
    
    def run(self):
        """تشغيل التطبيق"""
        self.window.mainloop()

if __name__ == "__main__":
    print("🚀 بدء اختبار النافذة الرئيسية مع التذييل المحسن...")
    app = SimpleMainApp()
    app.run()