#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تسجيل الدخول والنافذة الرئيسية
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from engineering_management_system import DatabaseManager, AuthenticationManager

class SimpleLoginTest:
    def __init__(self):
        self.db_manager = DatabaseManager("data/engineering_system.db")
        self.auth_manager = AuthenticationManager(self.db_manager)
        
    def show_login(self):
        """عرض نافذة تسجيل الدخول البسيطة"""
        self.login_window = ttk_bs.Window(
            title="تسجيل الدخول",
            themename="cosmo",
            size=(400, 300)
        )
        
        # حقل اسم المستخدم
        ttk_bs.Label(self.login_window, text="اسم المستخدم:").pack(pady=10)
        self.username_entry = ttk_bs.Entry(self.login_window, width=30)
        self.username_entry.pack(pady=5)
        self.username_entry.insert(0, "admin")
        
        # حقل كلمة المرور
        ttk_bs.Label(self.login_window, text="كلمة المرور:").pack(pady=10)
        self.password_entry = ttk_bs.Entry(self.login_window, width=30, show="*")
        self.password_entry.pack(pady=5)
        self.password_entry.insert(0, "admin123")
        
        # زر تسجيل الدخول
        login_btn = ttk_bs.Button(
            self.login_window,
            text="تسجيل الدخول",
            command=self.login
        )
        login_btn.pack(pady=20)
        
        self.login_window.mainloop()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get()
        password = self.password_entry.get()
        
        print(f"محاولة تسجيل الدخول: {username}")
        
        if self.auth_manager.authenticate(username, password):
            print("تسجيل الدخول نجح!")
            messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح")
            
            # إغلاق نافذة تسجيل الدخول
            self.login_window.destroy()
            
            # عرض النافذة الرئيسية
            self.show_main_window()
        else:
            print("فشل تسجيل الدخول!")
            messagebox.showerror("خطأ", "بيانات خاطئة")
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        print("بدء عرض النافذة الرئيسية...")
        
        try:
            self.main_window = ttk_bs.Window(
                title="النافذة الرئيسية",
                themename="cosmo",
                size=(800, 600)
            )
            
            # محتوى بسيط
            welcome_label = ttk_bs.Label(
                self.main_window,
                text=f"مرحباً {self.auth_manager.current_user['full_name']}!",
                font=("Arial", 16)
            )
            welcome_label.pack(pady=50)
            
            close_btn = ttk_bs.Button(
                self.main_window,
                text="إغلاق",
                command=self.main_window.destroy
            )
            close_btn.pack(pady=20)
            
            print("النافذة الرئيسية جاهزة...")
            self.main_window.mainloop()
            
        except Exception as e:
            print(f"خطأ في النافذة الرئيسية: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    app = SimpleLoginTest()
    app.show_login()