#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المستخدمين والصلاحيات - نسخة محسنة
User Management Module - Enhanced Version
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import hashlib
import re
from datetime import datetime

class UserManagementWindow:
    """نافذة إدارة المستخدمين"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.window = None
        self.users_tree = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط المحسنة"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.tree_font = ("Segoe UI", 10)

    def show(self):
        """عرض نافذة إدارة المستخدمين"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة المستخدمين")
        self.window.geometry("900x600")
        self.window.resizable(True, True)
        
        # التحقق من الصلاحيات
        if not self.auth_manager.has_permission('admin'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية للوصول إلى إدارة المستخدمين")
            self.window.destroy()
            return
        
        self.create_interface()
        self.load_users()
        
    def create_interface(self):
        """إنشاء واجهة إدارة المستخدمين"""
        # شريط الأدوات
        toolbar_frame = ttk_bs.Frame(self.window)
        toolbar_frame.pack(fill=X, padx=5, pady=5)
        
        ttk_bs.Button(
            toolbar_frame,
            text="مستخدم جديد",
            bootstyle=SUCCESS,
            command=self.add_user
        ).pack(side=LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="تعديل",
            bootstyle=WARNING,
            command=self.edit_user
        ).pack(side=LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="حذف",
            bootstyle=DANGER,
            command=self.delete_user
        ).pack(side=LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="تغيير كلمة المرور",
            bootstyle=INFO,
            command=self.change_password
        ).pack(side=LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="تحديث",
            bootstyle=SECONDARY,
            command=self.load_users
        ).pack(side=RIGHT, padx=2)
        
        # جدول المستخدمين
        tree_frame = ttk_bs.Frame(self.window)
        tree_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # إنشاء Treeview
        columns = ("id", "username", "full_name", "role", "email", "phone", "last_login", "is_active")
        self.users_tree = ttk_bs.Treeview(tree_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين
        headers = {
            "id": "الرقم",
            "username": "اسم المستخدم",
            "full_name": "الاسم الكامل",
            "role": "الصلاحية",
            "email": "البريد الإلكتروني",
            "phone": "الهاتف",
            "last_login": "آخر دخول",
            "is_active": "نشط"
        }
        
        for col in columns:
            self.users_tree.heading(col, text=headers[col])
            self.users_tree.column(col, width=100)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(tree_frame, orient=VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        self.users_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط النقر المزدوج بالتعديل
        self.users_tree.bind("<Double-1>", lambda e: self.edit_user())
    
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        # مسح البيانات الحالية
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        # تحميل البيانات من قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, full_name, role, email, phone, last_login, is_active
            FROM users
            ORDER BY full_name
        ''')
        
        users = cursor.fetchall()
        
        for user in users:
            # تنسيق آخر دخول
            last_login = user['last_login']
            if last_login:
                last_login = datetime.fromisoformat(last_login).strftime("%Y-%m-%d %H:%M")
            else:
                last_login = "لم يسجل دخول"
            
            # تنسيق الحالة
            is_active = "نشط" if user['is_active'] else "غير نشط"
            
            # تنسيق الصلاحية
            role_names = {
                'admin': 'مدير النظام',
                'manager': 'مدير',
                'engineer': 'مهندس',
                'technician': 'فني'
            }
            role_display = role_names.get(user['role'], user['role'])
            
            self.users_tree.insert("", "end", values=(
                user['id'],
                user['username'],
                user['full_name'],
                role_display,
                user['email'] or "",
                user['phone'] or "",
                last_login,
                is_active
            ))
        
        conn.close()
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserDialog(self.window, self.db_manager, "إضافة مستخدم جديد")
        if dialog.show():
            self.load_users()
    
    def edit_user(self):
        """تعديل مستخدم"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return
        
        user_id = self.users_tree.item(selected[0])['values'][0]
        
        # تحميل بيانات المستخدم
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
        user_data = cursor.fetchone()
        conn.close()
        
        if user_data:
            dialog = UserDialog(self.window, self.db_manager, "تعديل المستخدم", dict(user_data))
            if dialog.show():
                self.load_users()
    
    def delete_user(self):
        """حذف مستخدم"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return
        
        user_id = self.users_tree.item(selected[0])['values'][0]
        username = self.users_tree.item(selected[0])['values'][1]
        
        # منع حذف المستخدم الحالي
        if user_id == self.auth_manager.current_user['id']:
            messagebox.showerror("خطأ", "لا يمكن حذف المستخدم الحالي")
            return
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المستخدم '{username}'؟"):
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))
                conn.commit()
                messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح")
                self.load_users()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المستخدم: {str(e)}")
            finally:
                conn.close()
    
    def change_password(self):
        """تغيير كلمة المرور"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لتغيير كلمة المرور")
            return
        
        user_id = self.users_tree.item(selected[0])['values'][0]
        username = self.users_tree.item(selected[0])['values'][1]
        
        dialog = PasswordChangeDialog(self.window, self.db_manager, user_id, username)
        dialog.show()

class UserDialog:
    """نافذة إضافة/تعديل المستخدم"""
    
    def __init__(self, parent, db_manager, title, user_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.title = title
        self.user_data = user_data
        self.result = False
        self.window = None
        
    def show(self):
        """عرض النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("400x500")
        self.window.resizable(False, False)
        self.window.transient(self.parent)
        self.window.grab_set()
        
        self.create_form()
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
        
        self.window.wait_window()
        return self.result
    
    def create_form(self):
        """إنشاء نموذج المستخدم"""
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # اسم المستخدم
        ttk_bs.Label(main_frame, text="اسم المستخدم:").pack(anchor=W, pady=(0, 5))
        self.username_entry = ttk_bs.Entry(main_frame, width=30)
        self.username_entry.pack(pady=(0, 10))
        
        # كلمة المرور (فقط للمستخدم الجديد)
        if not self.user_data:
            ttk_bs.Label(main_frame, text="كلمة المرور:").pack(anchor=W, pady=(0, 5))
            self.password_entry = ttk_bs.Entry(main_frame, show="*", width=30)
            self.password_entry.pack(pady=(0, 10))
            
            ttk_bs.Label(main_frame, text="تأكيد كلمة المرور:").pack(anchor=W, pady=(0, 5))
            self.confirm_password_entry = ttk_bs.Entry(main_frame, show="*", width=30)
            self.confirm_password_entry.pack(pady=(0, 10))
        
        # الاسم الكامل
        ttk_bs.Label(main_frame, text="الاسم الكامل:").pack(anchor=W, pady=(0, 5))
        self.full_name_entry = ttk_bs.Entry(main_frame, width=30)
        self.full_name_entry.pack(pady=(0, 10))
        
        # الصلاحية
        ttk_bs.Label(main_frame, text="الصلاحية:").pack(anchor=W, pady=(0, 5))
        self.role_combo = ttk_bs.Combobox(main_frame, width=27, state="readonly")
        self.role_combo['values'] = ("admin", "manager", "engineer", "technician")
        self.role_combo.pack(pady=(0, 10))
        
        # البريد الإلكتروني
        ttk_bs.Label(main_frame, text="البريد الإلكتروني:").pack(anchor=W, pady=(0, 5))
        self.email_entry = ttk_bs.Entry(main_frame, width=30)
        self.email_entry.pack(pady=(0, 10))
        
        # الهاتف
        ttk_bs.Label(main_frame, text="الهاتف:").pack(anchor=W, pady=(0, 5))
        self.phone_entry = ttk_bs.Entry(main_frame, width=30)
        self.phone_entry.pack(pady=(0, 10))
        
        # حالة النشاط
        self.is_active_var = tk.BooleanVar(value=True)
        ttk_bs.Checkbutton(
            main_frame,
            text="المستخدم نشط",
            variable=self.is_active_var
        ).pack(pady=10)
        
        # ملء البيانات إذا كان تعديل
        if self.user_data:
            self.username_entry.insert(0, self.user_data.get('username', ''))
            self.full_name_entry.insert(0, self.user_data.get('full_name', ''))
            self.role_combo.set(self.user_data.get('role', ''))
            self.email_entry.insert(0, self.user_data.get('email', '') or '')
            self.phone_entry.insert(0, self.user_data.get('phone', '') or '')
            self.is_active_var.set(bool(self.user_data.get('is_active', True)))
        
        # الأزرار
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X, pady=20)
        
        ttk_bs.Button(
            button_frame,
            text="حفظ",
            bootstyle=SUCCESS,
            command=self.save_user
        ).pack(side=RIGHT, padx=(5, 0))
        
        ttk_bs.Button(
            button_frame,
            text="إلغاء",
            bootstyle=SECONDARY,
            command=self.window.destroy
        ).pack(side=RIGHT)
    
    def save_user(self):
        """حفظ بيانات المستخدم"""
        # التحقق من صحة البيانات
        username = self.username_entry.get().strip()
        full_name = self.full_name_entry.get().strip()
        role = self.role_combo.get()
        email = self.email_entry.get().strip()
        phone = self.phone_entry.get().strip()
        is_active = self.is_active_var.get()
        
        if not username or not full_name or not role:
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return
        
        # التحقق من كلمة المرور للمستخدم الجديد
        if not self.user_data:
            password = self.password_entry.get()
            confirm_password = self.confirm_password_entry.get()
            
            if not password:
                messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
                return
            
            if password != confirm_password:
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                return
            
            if len(password) < 6:
                messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                return
        
        # التحقق من صحة البريد الإلكتروني
        if email and not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            messagebox.showerror("خطأ", "البريد الإلكتروني غير صحيح")
            return
        
        # حفظ في قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            if self.user_data:  # تعديل
                cursor.execute('''
                    UPDATE users 
                    SET username=?, full_name=?, role=?, email=?, phone=?, is_active=?
                    WHERE id=?
                ''', (username, full_name, role, email or None, phone or None, is_active, self.user_data['id']))
            else:  # إضافة جديد
                password_hash = hashlib.sha256(password.encode()).hexdigest()
                cursor.execute('''
                    INSERT INTO users (username, password_hash, full_name, role, email, phone, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (username, password_hash, full_name, role, email or None, phone or None, is_active))
            
            conn.commit()
            messagebox.showinfo("نجح", "تم حفظ بيانات المستخدم بنجاح")
            self.result = True
            self.window.destroy()
            
        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "اسم المستخدم موجود مسبقاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
        finally:
            conn.close()

class PasswordChangeDialog:
    """نافذة تغيير كلمة المرور"""
    
    def __init__(self, parent, db_manager, user_id, username):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.username = username
        
    def show(self):
        """عرض نافذة تغيير كلمة المرور"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"تغيير كلمة المرور - {self.username}")
        self.window.geometry("350x200")
        self.window.resizable(False, False)
        self.window.transient(self.parent)
        self.window.grab_set()
        
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # كلمة المرور الجديدة
        ttk_bs.Label(main_frame, text="كلمة المرور الجديدة:").pack(anchor=W, pady=(0, 5))
        self.new_password_entry = ttk_bs.Entry(main_frame, show="*", width=30)
        self.new_password_entry.pack(pady=(0, 10))
        
        # تأكيد كلمة المرور
        ttk_bs.Label(main_frame, text="تأكيد كلمة المرور:").pack(anchor=W, pady=(0, 5))
        self.confirm_password_entry = ttk_bs.Entry(main_frame, show="*", width=30)
        self.confirm_password_entry.pack(pady=(0, 20))
        
        # الأزرار
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X)
        
        ttk_bs.Button(
            button_frame,
            text="تغيير",
            bootstyle=SUCCESS,
            command=self.change_password
        ).pack(side=RIGHT, padx=(5, 0))
        
        ttk_bs.Button(
            button_frame,
            text="إلغاء",
            bootstyle=SECONDARY,
            command=self.window.destroy
        ).pack(side=RIGHT)
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
    
    def change_password(self):
        """تغيير كلمة المرور"""
        new_password = self.new_password_entry.get()
        confirm_password = self.confirm_password_entry.get()
        
        if not new_password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور الجديدة")
            return
        
        if new_password != confirm_password:
            messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
            return
        
        if len(new_password) < 6:
            messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return
        
        # تحديث كلمة المرور في قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            password_hash = hashlib.sha256(new_password.encode()).hexdigest()
            cursor.execute("UPDATE users SET password_hash = ? WHERE id = ?", (password_hash, self.user_id))
            conn.commit()
            
            messagebox.showinfo("نجح", "تم تغيير كلمة المرور بنجاح")
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تغيير كلمة المرور: {str(e)}")
        finally:
            conn.close()
