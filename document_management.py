#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المستندات والملفات
Document Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import os
import shutil
from datetime import datetime
from pathlib import Path

class DocumentManagementWindow:
    """نافذة إدارة المستندات"""
    
    def __init__(self, parent, db_manager, auth_manager, entity_type, entity_id, entity_name):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.entity_type = entity_type  # project, building, asset, maintenance
        self.entity_id = entity_id
        self.entity_name = entity_name
        self.window = None
        self.docs_tree = None
        
    def show(self):
        """عرض نافذة إدارة المستندات"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"إدارة المستندات - {self.entity_name}")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        self.create_interface()
        self.load_documents()
        
    def create_interface(self):
        """إنشاء واجهة إدارة المستندات"""
        # شريط الأدوات
        toolbar_frame = ttk_bs.Frame(self.window)
        toolbar_frame.pack(fill=X, padx=5, pady=5)
        
        ttk_bs.Button(
            toolbar_frame,
            text="رفع مستند",
            bootstyle=SUCCESS,
            command=self.upload_document
        ).pack(side=LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="تحميل",
            bootstyle=INFO,
            command=self.download_document
        ).pack(side=LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="عرض",
            bootstyle=PRIMARY,
            command=self.view_document
        ).pack(side=LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="حذف",
            bootstyle=DANGER,
            command=self.delete_document
        ).pack(side=LEFT, padx=2)
        
        ttk_bs.Button(
            toolbar_frame,
            text="تحديث",
            bootstyle=SECONDARY,
            command=self.load_documents
        ).pack(side=RIGHT, padx=2)
        
        # جدول المستندات
        tree_frame = ttk_bs.Frame(self.window)
        tree_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        columns = ("id", "document_name", "document_type", "file_size", "uploaded_by", "uploaded_at")
        self.docs_tree = ttk_bs.Treeview(tree_frame, columns=columns, show="headings", height=20)
        
        headers = {
            "id": "الرقم",
            "document_name": "اسم المستند",
            "document_type": "النوع",
            "file_size": "الحجم",
            "uploaded_by": "رفع بواسطة",
            "uploaded_at": "تاريخ الرفع"
        }
        
        for col in columns:
            self.docs_tree.heading(col, text=headers[col])
            if col == "document_name":
                self.docs_tree.column(col, width=200)
            elif col == "file_size":
                self.docs_tree.column(col, width=100)
            else:
                self.docs_tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(tree_frame, orient=VERTICAL, command=self.docs_tree.yview)
        self.docs_tree.configure(yscrollcommand=scrollbar.set)
        
        self.docs_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط النقر المزدوج بالعرض
        self.docs_tree.bind("<Double-1>", lambda e: self.view_document())
    
    def load_documents(self):
        """تحميل قائمة المستندات"""
        # مسح البيانات الحالية
        for item in self.docs_tree.get_children():
            self.docs_tree.delete(item)
        
        # تحميل البيانات من قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT d.id, d.document_name, d.document_type, d.file_size, 
                   d.uploaded_at, u.full_name as uploaded_by_name
            FROM documents d
            LEFT JOIN users u ON d.uploaded_by = u.id
            WHERE d.entity_type = ? AND d.entity_id = ?
            ORDER BY d.uploaded_at DESC
        ''', (self.entity_type, self.entity_id))
        
        documents = cursor.fetchall()
        
        for doc in documents:
            # تنسيق حجم الملف
            file_size = doc['file_size']
            if file_size:
                if file_size < 1024:
                    size_display = f"{file_size} بايت"
                elif file_size < 1024 * 1024:
                    size_display = f"{file_size / 1024:.1f} كيلوبايت"
                else:
                    size_display = f"{file_size / (1024 * 1024):.1f} ميجابايت"
            else:
                size_display = "غير محدد"
            
            # تنسيق التاريخ
            uploaded_at = doc['uploaded_at']
            if uploaded_at:
                uploaded_at = datetime.fromisoformat(uploaded_at).strftime("%Y-%m-%d %H:%M")
            else:
                uploaded_at = "غير محدد"
            
            self.docs_tree.insert("", "end", values=(
                doc['id'],
                doc['document_name'],
                doc['document_type'] or "غير محدد",
                size_display,
                doc['uploaded_by_name'] or "غير محدد",
                uploaded_at
            ))
        
        conn.close()
    
    def upload_document(self):
        """رفع مستند جديد"""
        if not self.auth_manager.has_permission('technician'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لرفع المستندات")
            return
        
        # اختيار الملف
        file_path = filedialog.askopenfilename(
            title="اختر المستند",
            filetypes=[
                ("جميع الملفات", "*.*"),
                ("ملفات PDF", "*.pdf"),
                ("ملفات Word", "*.doc;*.docx"),
                ("ملفات Excel", "*.xls;*.xlsx"),
                ("ملفات الصور", "*.jpg;*.jpeg;*.png;*.gif;*.bmp"),
                ("ملفات CAD", "*.dwg;*.dxf"),
                ("ملفات النصوص", "*.txt")
            ]
        )
        
        if not file_path:
            return
        
        # الحصول على معلومات الملف
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        file_ext = os.path.splitext(file_name)[1].lower()
        
        # تحديد نوع المستند
        document_types = {
            '.pdf': 'PDF',
            '.doc': 'Word',
            '.docx': 'Word',
            '.xls': 'Excel',
            '.xlsx': 'Excel',
            '.jpg': 'صورة',
            '.jpeg': 'صورة',
            '.png': 'صورة',
            '.gif': 'صورة',
            '.bmp': 'صورة',
            '.dwg': 'CAD',
            '.dxf': 'CAD',
            '.txt': 'نص'
        }
        
        document_type = document_types.get(file_ext, 'أخرى')
        
        # إنشاء مجلد المستندات إذا لم يكن موجوداً
        docs_dir = Path("documents") / self.entity_type / str(self.entity_id)
        docs_dir.mkdir(parents=True, exist_ok=True)
        
        # نسخ الملف
        destination_path = docs_dir / file_name
        
        # التحقق من وجود ملف بنفس الاسم
        counter = 1
        original_name = file_name
        while destination_path.exists():
            name, ext = os.path.splitext(original_name)
            file_name = f"{name}_{counter}{ext}"
            destination_path = docs_dir / file_name
            counter += 1
        
        try:
            shutil.copy2(file_path, destination_path)
            
            # حفظ في قاعدة البيانات
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO documents (entity_type, entity_id, document_name, 
                                     document_type, file_path, file_size, uploaded_by)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (self.entity_type, self.entity_id, file_name, document_type, 
                  str(destination_path), file_size, self.auth_manager.current_user['id']))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", "تم رفع المستند بنجاح")
            self.load_documents()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في رفع المستند: {str(e)}")
    
    def download_document(self):
        """تحميل مستند"""
        selected = self.docs_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستند للتحميل")
            return
        
        doc_id = self.docs_tree.item(selected[0])['values'][0]
        
        # الحصول على مسار الملف
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT document_name, file_path FROM documents WHERE id = ?", (doc_id,))
        doc_data = cursor.fetchone()
        conn.close()
        
        if not doc_data:
            messagebox.showerror("خطأ", "لم يتم العثور على المستند")
            return
        
        source_path = doc_data['file_path']
        if not os.path.exists(source_path):
            messagebox.showerror("خطأ", "الملف غير موجود على النظام")
            return
        
        # اختيار مكان الحفظ
        save_path = filedialog.asksaveasfilename(
            title="حفظ المستند",
            initialname=doc_data['document_name'],
            defaultextension=os.path.splitext(doc_data['document_name'])[1]
        )
        
        if save_path:
            try:
                shutil.copy2(source_path, save_path)
                messagebox.showinfo("نجح", "تم تحميل المستند بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل المستند: {str(e)}")
    
    def view_document(self):
        """عرض مستند"""
        selected = self.docs_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستند للعرض")
            return
        
        doc_id = self.docs_tree.item(selected[0])['values'][0]
        
        # الحصول على مسار الملف
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT file_path FROM documents WHERE id = ?", (doc_id,))
        doc_data = cursor.fetchone()
        conn.close()
        
        if not doc_data:
            messagebox.showerror("خطأ", "لم يتم العثور على المستند")
            return
        
        file_path = doc_data['file_path']
        if not os.path.exists(file_path):
            messagebox.showerror("خطأ", "الملف غير موجود على النظام")
            return
        
        try:
            # فتح الملف بالبرنامج الافتراضي
            os.startfile(file_path)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح المستند: {str(e)}")
    
    def delete_document(self):
        """حذف مستند"""
        selected = self.docs_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستند للحذف")
            return
        
        if not self.auth_manager.has_permission('engineer'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لحذف المستندات")
            return
        
        doc_id = self.docs_tree.item(selected[0])['values'][0]
        doc_name = self.docs_tree.item(selected[0])['values'][1]
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المستند '{doc_name}'؟"):
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            try:
                # الحصول على مسار الملف
                cursor.execute("SELECT file_path FROM documents WHERE id = ?", (doc_id,))
                doc_data = cursor.fetchone()
                
                if doc_data and os.path.exists(doc_data['file_path']):
                    os.remove(doc_data['file_path'])
                
                # حذف من قاعدة البيانات
                cursor.execute("DELETE FROM documents WHERE id = ?", (doc_id,))
                conn.commit()
                
                messagebox.showinfo("نجح", "تم حذف المستند بنجاح")
                self.load_documents()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المستند: {str(e)}")
            finally:
                conn.close()
