#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة عدم فتح الشاشة الرئيسية
Debug Main Window Issue
"""

import sys
import os
import traceback

# إضافة المسار الحالي لـ Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """اختبار استيراد المكتبات"""
    try:
        print("🔍 اختبار استيراد المكتبات...")
        
        import tkinter as tk
        print("✅ tkinter - تم")
        
        import ttkbootstrap as ttk_bs
        print("✅ ttkbootstrap - تم")
        
        from engineering_management_system import DatabaseManager, AuthenticationManager
        print("✅ engineering_management_system - تم")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        print("\n🔍 اختبار قاعدة البيانات...")
        
        from engineering_management_system import DatabaseManager, AuthenticationManager
        
        db_manager = DatabaseManager("data/engineering_system.db")
        print("✅ إنشاء DatabaseManager - تم")
        
        auth_manager = AuthenticationManager(db_manager)
        print("✅ إنشاء AuthenticationManager - تم")
        
        # اختبار تسجيل الدخول
        result = auth_manager.authenticate("admin", "admin123")
        if result:
            print("✅ تسجيل الدخول - تم")
            print(f"   المستخدم: {auth_manager.current_user}")
        else:
            print("❌ تسجيل الدخول - فشل")
            
        return True, auth_manager
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        traceback.print_exc()
        return False, None

def test_main_window_creation(auth_manager):
    """اختبار إنشاء النافذة الرئيسية"""
    try:
        print("\n🔍 اختبار إنشاء النافذة الرئيسية...")
        
        from main_gui import MainApplication
        
        app = MainApplication()
        print("✅ إنشاء MainApplication - تم")
        
        # تعيين auth_manager
        app.auth_manager = auth_manager
        print("✅ تعيين auth_manager - تم")
        
        # اختبار إنشاء النافذة بدون عرضها
        print("🔍 اختبار show_main_window...")
        
        import ttkbootstrap as ttk_bs
        
        # إنشاء نافذة اختبار
        test_window = ttk_bs.Window(
            title="🧪 اختبار النافذة الرئيسية",
            themename="cosmo",
            size=(800, 600)
        )
        
        app.window = test_window
        print("✅ إنشاء النافذة - تم")
        
        # اختبار الدوال واحدة تلو الأخرى
        try:
            app.apply_custom_styles()
            print("✅ apply_custom_styles - تم")
        except Exception as e:
            print(f"❌ apply_custom_styles - فشل: {e}")
        
        try:
            app.create_menu()
            print("✅ create_menu - تم")
        except Exception as e:
            print(f"❌ create_menu - فشل: {e}")
        
        try:
            app.create_main_interface()
            print("✅ create_main_interface - تم")
        except Exception as e:
            print(f"❌ create_main_interface - فشل: {e}")
            traceback.print_exc()
        
        try:
            app.create_footer()
            print("✅ create_footer - تم")
        except Exception as e:
            print(f"❌ create_footer - فشل: {e}")
            traceback.print_exc()
        
        try:
            app.update_footer()
            print("✅ update_footer - تم")
        except Exception as e:
            print(f"❌ update_footer - فشل: {e}")
        
        print("\n🎉 جميع الاختبارات نجحت! النافذة جاهزة للعرض.")
        
        # عرض النافذة لثواني قليلة
        test_window.after(3000, test_window.destroy)  # إغلاق بعد 3 ثواني
        test_window.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة الرئيسية: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🧪 بدء تشخيص مشكلة النافذة الرئيسية...")
    print("=" * 50)
    
    # اختبار الاستيراد
    if not test_imports():
        return
    
    # اختبار قاعدة البيانات
    db_success, auth_manager = test_database()
    if not db_success:
        return
    
    # اختبار النافذة الرئيسية
    if test_main_window_creation(auth_manager):
        print("\n🎉 التشخيص مكتمل - لا توجد مشاكل!")
    else:
        print("\n❌ تم العثور على مشاكل - راجع الأخطاء أعلاه")

if __name__ == "__main__":
    main()