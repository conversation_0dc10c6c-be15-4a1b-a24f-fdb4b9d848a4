#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المشاريع الهندسية
Project Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

class ProjectManagementWindow:
    """نافذة إدارة المشاريع"""

    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.window = None
        self.projects_tree = None

    def show(self):
        """عرض نافذة إدارة المشاريع"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة المشاريع الهندسية")
        self.window.geometry("1000x700")
        self.window.resizable(True, True)

        self.create_interface()
        self.load_projects()

    def create_interface(self):
        """إنشاء واجهة إدارة المشاريع"""
        # شريط الأدوات
        toolbar_frame = ttk_bs.Frame(self.window)
        toolbar_frame.pack(fill=X, padx=5, pady=5)

        ttk_bs.Button(
            toolbar_frame,
            text="مشروع جديد",
            bootstyle=SUCCESS,
            command=self.add_project
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            toolbar_frame,
            text="تعديل",
            bootstyle=WARNING,
            command=self.edit_project
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            toolbar_frame,
            text="حذف",
            bootstyle=DANGER,
            command=self.delete_project
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            toolbar_frame,
            text="عرض التفاصيل",
            bootstyle=INFO,
            command=self.view_project_details
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            toolbar_frame,
            text="إدارة المراحل",
            bootstyle=PRIMARY,
            command=self.manage_phases
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            toolbar_frame,
            text="المستندات",
            bootstyle=SECONDARY,
            command=self.manage_documents
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            toolbar_frame,
            text="تحديث",
            bootstyle=LIGHT,
            command=self.load_projects
        ).pack(side=RIGHT, padx=2)

        # إطار البحث والفلترة
        search_frame = ttk_bs.LabelFrame(self.window, text="البحث والفلترة", padding=5)
        search_frame.pack(fill=X, padx=5, pady=5)

        # حقل البحث
        ttk_bs.Label(search_frame, text="البحث:").pack(side=LEFT, padx=2)
        self.search_entry = ttk_bs.Entry(search_frame, width=20)
        self.search_entry.pack(side=LEFT, padx=2)
        self.search_entry.bind('<KeyRelease>', self.filter_projects)

        # فلتر الحالة
        ttk_bs.Label(search_frame, text="الحالة:").pack(side=LEFT, padx=(10, 2))
        self.status_filter = ttk.Combobox(search_frame, width=15, state="readonly")
        self.status_filter['values'] = ("الكل", "تخطيط", "تصميم", "ترسية", "تنفيذ", "تسليم", "مكتمل")
        self.status_filter.set("الكل")
        self.status_filter.pack(side=LEFT, padx=2)
        self.status_filter.bind('<<ComboboxSelected>>', self.filter_projects)

        # جدول المشاريع
        tree_frame = ttk_bs.Frame(self.window)
        tree_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)

        # إنشاء Treeview
        columns = ("id", "name", "location", "type", "cost", "contractor", "start_date", "end_date", "status", "progress")
        self.projects_tree = ttk_bs.Treeview(tree_frame, columns=columns, show="headings", height=20)

        # تعريف العناوين
        headers = {
            "id": "الرقم",
            "name": "اسم المشروع",
            "location": "الموقع",
            "type": "النوع",
            "cost": "الكلفة",
            "contractor": "المقاول",
            "start_date": "تاريخ البداية",
            "end_date": "تاريخ الانتهاء",
            "status": "الحالة",
            "progress": "نسبة الإنجاز"
        }

        for col in columns:
            self.projects_tree.heading(col, text=headers[col])
            if col == "name":
                self.projects_tree.column(col, width=200)
            elif col == "cost":
                self.projects_tree.column(col, width=100)
            else:
                self.projects_tree.column(col, width=120)

        # شريط التمرير
        scrollbar_y = ttk_bs.Scrollbar(tree_frame, orient=VERTICAL, command=self.projects_tree.yview)
        scrollbar_x = ttk_bs.Scrollbar(tree_frame, orient=HORIZONTAL, command=self.projects_tree.xview)
        self.projects_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        self.projects_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar_y.pack(side=RIGHT, fill=Y)
        scrollbar_x.pack(side=BOTTOM, fill=X)

        # ربط النقر المزدوج بعرض التفاصيل
        self.projects_tree.bind("<Double-1>", lambda e: self.view_project_details())

    def load_projects(self):
        """تحميل قائمة المشاريع"""
        # مسح البيانات الحالية
        for item in self.projects_tree.get_children():
            self.projects_tree.delete(item)

        # تحميل البيانات من قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, name, location, project_type, cost, contractor,
                   start_date, end_date, status, progress_percentage
            FROM projects
            ORDER BY created_at DESC
        ''')

        projects = cursor.fetchall()

        for project in projects:
            # تنسيق التواريخ
            start_date = project['start_date'] if project['start_date'] else ""
            end_date = project['end_date'] if project['end_date'] else ""

            # تنسيق الكلفة
            cost = f"{project['cost']:,.0f}" if project['cost'] else ""

            # تنسيق الحالة
            status_names = {
                'planning': 'تخطيط',
                'design': 'تصميم',
                'tender': 'ترسية',
                'execution': 'تنفيذ',
                'delivery': 'تسليم',
                'completed': 'مكتمل'
            }
            status_display = status_names.get(project['status'], project['status'])

            # تنسيق نسبة الإنجاز
            progress = f"{project['progress_percentage']:.1f}%" if project['progress_percentage'] else "0%"

            self.projects_tree.insert("", "end", values=(
                project['id'],
                project['name'],
                project['location'] or "",
                project['project_type'] or "",
                cost,
                project['contractor'] or "",
                start_date,
                end_date,
                status_display,
                progress
            ))

        conn.close()

    def filter_projects(self, event=None):
        """فلترة المشاريع حسب البحث والحالة"""
        search_text = self.search_entry.get().lower()
        status_filter = self.status_filter.get()

        # مسح البيانات الحالية
        for item in self.projects_tree.get_children():
            self.projects_tree.delete(item)

        # تحميل البيانات مع الفلترة
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        query = '''
            SELECT id, name, location, project_type, cost, contractor,
                   start_date, end_date, status, progress_percentage
            FROM projects
            WHERE 1=1
        '''
        params = []

        if search_text:
            query += " AND (LOWER(name) LIKE ? OR LOWER(location) LIKE ? OR LOWER(contractor) LIKE ?)"
            search_param = f"%{search_text}%"
            params.extend([search_param, search_param, search_param])

        if status_filter != "الكل":
            status_map = {
                "تخطيط": "planning",
                "تصميم": "design",
                "ترسية": "tender",
                "تنفيذ": "execution",
                "تسليم": "delivery",
                "مكتمل": "completed"
            }
            if status_filter in status_map:
                query += " AND status = ?"
                params.append(status_map[status_filter])

        query += " ORDER BY created_at DESC"

        cursor.execute(query, params)
        projects = cursor.fetchall()

        for project in projects:
            # تنسيق البيانات (نفس الكود السابق)
            start_date = project['start_date'] if project['start_date'] else ""
            end_date = project['end_date'] if project['end_date'] else ""
            cost = f"{project['cost']:,.0f}" if project['cost'] else ""

            status_names = {
                'planning': 'تخطيط',
                'design': 'تصميم',
                'tender': 'ترسية',
                'execution': 'تنفيذ',
                'delivery': 'تسليم',
                'completed': 'مكتمل'
            }
            status_display = status_names.get(project['status'], project['status'])
            progress = f"{project['progress_percentage']:.1f}%" if project['progress_percentage'] else "0%"

            self.projects_tree.insert("", "end", values=(
                project['id'],
                project['name'],
                project['location'] or "",
                project['project_type'] or "",
                cost,
                project['contractor'] or "",
                start_date,
                end_date,
                status_display,
                progress
            ))

        conn.close()

    def add_project(self):
        """إضافة مشروع جديد"""
        if not self.auth_manager.has_permission('engineer'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإضافة المشاريع")
            return

        dialog = ProjectDialog(self.window, self.db_manager, self.auth_manager, "إضافة مشروع جديد")
        if dialog.show():
            self.load_projects()

    def edit_project(self):
        """تعديل مشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للتعديل")
            return

        if not self.auth_manager.has_permission('engineer'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لتعديل المشاريع")
            return

        project_id = self.projects_tree.item(selected[0])['values'][0]

        # تحميل بيانات المشروع
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM projects WHERE id = ?", (project_id,))
        project_data = cursor.fetchone()
        conn.close()

        if project_data:
            dialog = ProjectDialog(self.window, self.db_manager, self.auth_manager, "تعديل المشروع", dict(project_data))
            if dialog.show():
                self.load_projects()

    def delete_project(self):
        """حذف مشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للحذف")
            return

        if not self.auth_manager.has_permission('manager'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لحذف المشاريع")
            return

        project_id = self.projects_tree.item(selected[0])['values'][0]
        project_name = self.projects_tree.item(selected[0])['values'][1]

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المشروع '{project_name}'؟\nسيتم حذف جميع البيانات المرتبطة به."):
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            try:
                # حذف المراحل والمستندات المرتبطة
                cursor.execute("DELETE FROM project_phases WHERE project_id = ?", (project_id,))
                cursor.execute("DELETE FROM documents WHERE entity_type = 'project' AND entity_id = ?", (project_id,))
                cursor.execute("DELETE FROM projects WHERE id = ?", (project_id,))

                conn.commit()
                messagebox.showinfo("نجح", "تم حذف المشروع بنجاح")
                self.load_projects()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المشروع: {str(e)}")
            finally:
                conn.close()

    def view_project_details(self):
        """عرض تفاصيل المشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لعرض التفاصيل")
            return

        project_id = self.projects_tree.item(selected[0])['values'][0]

        # تحميل بيانات المشروع
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM projects WHERE id = ?", (project_id,))
        project_data = cursor.fetchone()
        conn.close()

        if project_data:
            details_window = ProjectDetailsWindow(self.window, self.db_manager, dict(project_data))
            details_window.show()

    def manage_phases(self):
        """إدارة مراحل المشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لإدارة المراحل")
            return

        project_id = self.projects_tree.item(selected[0])['values'][0]
        project_name = self.projects_tree.item(selected[0])['values'][1]

        phases_window = ProjectPhasesWindow(self.window, self.db_manager, self.auth_manager, project_id, project_name)
        phases_window.show()

    def manage_documents(self):
        """إدارة مستندات المشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لإدارة المستندات")
            return

        project_id = self.projects_tree.item(selected[0])['values'][0]
        project_name = self.projects_tree.item(selected[0])['values'][1]

        from document_management import DocumentManagementWindow
        docs_window = DocumentManagementWindow(self.window, self.db_manager, self.auth_manager, 'project', project_id, project_name)
        docs_window.show()

class ProjectDialog:
    """نافذة إضافة/تعديل المشروع"""

    def __init__(self, parent, db_manager, auth_manager, title, project_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.title = title
        self.project_data = project_data
        self.result = False
        self.window = None

    def show(self):
        """عرض النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("600x700")
        self.window.resizable(False, False)
        self.window.transient(self.parent)
        self.window.grab_set()

        self.create_form()

        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")

        self.window.wait_window()
        return self.result

    def create_form(self):
        """إنشاء نموذج المشروع"""
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # اسم المشروع
        ttk_bs.Label(main_frame, text="اسم المشروع: *").pack(anchor=W, pady=(0, 5))
        self.name_entry = ttk_bs.Entry(main_frame, width=50)
        self.name_entry.pack(pady=(0, 10))

        # وصف المشروع
        ttk_bs.Label(main_frame, text="وصف المشروع:").pack(anchor=W, pady=(0, 5))
        self.description_text = tk.Text(main_frame, height=4, width=50)
        self.description_text.pack(pady=(0, 10))

        # الموقع
        ttk_bs.Label(main_frame, text="الموقع:").pack(anchor=W, pady=(0, 5))
        self.location_entry = ttk_bs.Entry(main_frame, width=50)
        self.location_entry.pack(pady=(0, 10))

        # نوع المشروع
        ttk_bs.Label(main_frame, text="نوع المشروع:").pack(anchor=W, pady=(0, 5))
        self.type_combo = ttk.Combobox(main_frame, width=47, state="readonly")
        self.type_combo['values'] = ("مباني سكنية", "مباني تجارية", "مباني إدارية", "مشاريع بنية تحتية", "مشاريع صناعية", "أخرى")
        self.type_combo.pack(pady=(0, 10))

        # الكلفة
        ttk_bs.Label(main_frame, text="الكلفة المقدرة:").pack(anchor=W, pady=(0, 5))
        self.cost_entry = ttk_bs.Entry(main_frame, width=50)
        self.cost_entry.pack(pady=(0, 10))

        # المقاول
        ttk_bs.Label(main_frame, text="المقاول:").pack(anchor=W, pady=(0, 5))
        self.contractor_entry = ttk_bs.Entry(main_frame, width=50)
        self.contractor_entry.pack(pady=(0, 10))

        # التواريخ
        dates_frame = ttk_bs.Frame(main_frame)
        dates_frame.pack(fill=X, pady=10)

        # تاريخ البداية
        start_frame = ttk_bs.Frame(dates_frame)
        start_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 5))
        ttk_bs.Label(start_frame, text="تاريخ البداية:").pack(anchor=W)
        self.start_date_entry = ttk_bs.DateEntry(start_frame, dateformat="%Y-%m-%d")
        self.start_date_entry.pack(fill=X)

        # تاريخ الانتهاء المتوقع
        end_frame = ttk_bs.Frame(dates_frame)
        end_frame.pack(side=LEFT, fill=X, expand=True, padx=(5, 0))
        ttk_bs.Label(end_frame, text="تاريخ الانتهاء المتوقع:").pack(anchor=W)
        self.end_date_entry = ttk_bs.DateEntry(end_frame, dateformat="%Y-%m-%d")
        self.end_date_entry.pack(fill=X)

        # الحالة
        ttk_bs.Label(main_frame, text="حالة المشروع:").pack(anchor=W, pady=(10, 5))
        self.status_combo = ttk.Combobox(main_frame, width=47, state="readonly")
        self.status_combo['values'] = ("planning", "design", "tender", "execution", "delivery", "completed")
        self.status_combo.set("planning")
        self.status_combo.pack(pady=(0, 10))

        # نسبة الإنجاز
        ttk_bs.Label(main_frame, text="نسبة الإنجاز (%):").pack(anchor=W, pady=(0, 5))
        self.progress_var = tk.DoubleVar(value=0)
        self.progress_scale = ttk_bs.Scale(
            main_frame,
            from_=0,
            to=100,
            orient=HORIZONTAL,
            variable=self.progress_var,
            length=400
        )
        self.progress_scale.pack(pady=(0, 5))

        self.progress_label = ttk_bs.Label(main_frame, text="0%")
        self.progress_label.pack(pady=(0, 10))

        # ربط تحديث النسبة
        self.progress_var.trace('w', self.update_progress_label)

        # ملء البيانات إذا كان تعديل
        if self.project_data:
            self.name_entry.insert(0, self.project_data.get('name', ''))
            self.description_text.insert('1.0', self.project_data.get('description', '') or '')
            self.location_entry.insert(0, self.project_data.get('location', '') or '')
            self.type_combo.set(self.project_data.get('project_type', '') or '')
            self.cost_entry.insert(0, str(self.project_data.get('cost', '') or ''))
            self.contractor_entry.insert(0, self.project_data.get('contractor', '') or '')

            if self.project_data.get('start_date'):
                self.start_date_entry.entry.delete(0, tk.END)
                self.start_date_entry.entry.insert(0, self.project_data['start_date'])

            if self.project_data.get('end_date'):
                self.end_date_entry.entry.delete(0, tk.END)
                self.end_date_entry.entry.insert(0, self.project_data['end_date'])

            self.status_combo.set(self.project_data.get('status', 'planning'))
            self.progress_var.set(self.project_data.get('progress_percentage', 0))

        # الأزرار
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X, pady=20)

        ttk_bs.Button(
            button_frame,
            text="حفظ",
            bootstyle=SUCCESS,
            command=self.save_project
        ).pack(side=RIGHT, padx=(5, 0))

        ttk_bs.Button(
            button_frame,
            text="إلغاء",
            bootstyle=SECONDARY,
            command=self.window.destroy
        ).pack(side=RIGHT)



    def update_progress_label(self, *args):
        """تحديث تسمية نسبة الإنجاز"""
        progress = self.progress_var.get()
        self.progress_label.config(text=f"{progress:.1f}%")

    def save_project(self):
        """حفظ بيانات المشروع"""
        # التحقق من صحة البيانات
        name = self.name_entry.get().strip()
        description = self.description_text.get('1.0', tk.END).strip()
        location = self.location_entry.get().strip()
        project_type = self.type_combo.get()
        cost_str = self.cost_entry.get().strip()
        contractor = self.contractor_entry.get().strip()
        start_date = self.start_date_entry.entry.get()
        end_date = self.end_date_entry.entry.get()
        status = self.status_combo.get()
        progress = self.progress_var.get()

        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
            return

        # التحقق من الكلفة
        cost = None
        if cost_str:
            try:
                cost = float(cost_str.replace(',', ''))
            except ValueError:
                messagebox.showerror("خطأ", "الكلفة يجب أن تكون رقماً")
                return

        # حفظ في قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if self.project_data:  # تعديل
                cursor.execute('''
                    UPDATE projects
                    SET name=?, description=?, location=?, project_type=?, cost=?,
                        contractor=?, start_date=?, end_date=?, status=?,
                        progress_percentage=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                ''', (name, description or None, location or None, project_type or None,
                      cost, contractor or None, start_date or None, end_date or None,
                      status, progress, self.project_data['id']))
            else:  # إضافة جديد
                cursor.execute('''
                    INSERT INTO projects (name, description, location, project_type, cost,
                                        contractor, start_date, end_date, status,
                                        progress_percentage, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (name, description or None, location or None, project_type or None,
                      cost, contractor or None, start_date or None, end_date or None,
                      status, progress, self.auth_manager.current_user['id']))

            conn.commit()
            messagebox.showinfo("نجح", "تم حفظ بيانات المشروع بنجاح")
            self.result = True
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
        finally:
            conn.close()


class ProjectDetailsWindow:
    """نافذة عرض تفاصيل المشروع"""

    def __init__(self, parent, db_manager, project_data):
        self.parent = parent
        self.db_manager = db_manager
        self.project_data = project_data
        self.window = None

    def show(self):
        """عرض نافذة التفاصيل"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"تفاصيل المشروع - {self.project_data['name']}")
        self.window.geometry("800x600")
        self.window.resizable(True, True)

        self.create_interface()

    def create_interface(self):
        """إنشاء واجهة عرض التفاصيل"""
        # دفتر الملاحظات للتبويبات
        notebook = ttk_bs.Notebook(self.window)
        notebook.pack(fill=BOTH, expand=True, padx=10, pady=10)

        # تبويب المعلومات الأساسية
        info_frame = ttk_bs.Frame(notebook)
        notebook.add(info_frame, text="المعلومات الأساسية")

        # تبويب المراحل
        phases_frame = ttk_bs.Frame(notebook)
        notebook.add(phases_frame, text="المراحل")

        # تبويب المستندات
        docs_frame = ttk_bs.Frame(notebook)
        notebook.add(docs_frame, text="المستندات")

        self.create_info_tab(info_frame)
        self.create_phases_tab(phases_frame)
        self.create_docs_tab(docs_frame)

    def create_info_tab(self, parent):
        """إنشاء تبويب المعلومات الأساسية"""
        # إطار التمرير
        canvas = tk.Canvas(parent)
        scrollbar = ttk_bs.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk_bs.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        main_frame = ttk_bs.Frame(scrollable_frame, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # عرض البيانات
        fields = [
            ("اسم المشروع", self.project_data.get('name', '')),
            ("الوصف", self.project_data.get('description', '') or 'غير محدد'),
            ("الموقع", self.project_data.get('location', '') or 'غير محدد'),
            ("نوع المشروع", self.project_data.get('project_type', '') or 'غير محدد'),
            ("الكلفة المقدرة", f"{self.project_data.get('cost', 0):,.0f}" if self.project_data.get('cost') else 'غير محدد'),
            ("المقاول", self.project_data.get('contractor', '') or 'غير محدد'),
            ("تاريخ البداية", self.project_data.get('start_date', '') or 'غير محدد'),
            ("تاريخ الانتهاء المتوقع", self.project_data.get('end_date', '') or 'غير محدد'),
            ("تاريخ الانتهاء الفعلي", self.project_data.get('actual_end_date', '') or 'لم ينته بعد'),
            ("الحالة", self.get_status_display()),
            ("نسبة الإنجاز", f"{self.project_data.get('progress_percentage', 0):.1f}%"),
            ("تاريخ الإنشاء", self.project_data.get('created_at', '') or 'غير محدد'),
            ("آخر تحديث", self.project_data.get('updated_at', '') or 'غير محدد')
        ]

        for label, value in fields:
            field_frame = ttk_bs.Frame(main_frame)
            field_frame.pack(fill=X, pady=5)

            ttk_bs.Label(field_frame, text=f"{label}:", font=("Arial", 10, "bold")).pack(side=LEFT, anchor=W)
            ttk_bs.Label(field_frame, text=str(value), font=("Arial", 10)).pack(side=LEFT, padx=(10, 0))

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_phases_tab(self, parent):
        """إنشاء تبويب المراحل"""
        # شريط الأدوات
        toolbar_frame = ttk_bs.Frame(parent)
        toolbar_frame.pack(fill=X, padx=5, pady=5)

        ttk_bs.Button(
            toolbar_frame,
            text="تحديث",
            bootstyle=SECONDARY,
            command=lambda: self.load_phases()
        ).pack(side=RIGHT)

        # جدول المراحل
        tree_frame = ttk_bs.Frame(parent)
        tree_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)

        columns = ("phase_name", "start_date", "end_date", "status", "progress")
        self.phases_tree = ttk_bs.Treeview(tree_frame, columns=columns, show="headings", height=15)

        headers = {
            "phase_name": "اسم المرحلة",
            "start_date": "تاريخ البداية",
            "end_date": "تاريخ الانتهاء",
            "status": "الحالة",
            "progress": "نسبة الإنجاز"
        }

        for col in columns:
            self.phases_tree.heading(col, text=headers[col])
            self.phases_tree.column(col, width=150)

        scrollbar_phases = ttk_bs.Scrollbar(tree_frame, orient=VERTICAL, command=self.phases_tree.yview)
        self.phases_tree.configure(yscrollcommand=scrollbar_phases.set)

        self.phases_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar_phases.pack(side=RIGHT, fill=Y)

        self.load_phases()

    def create_docs_tab(self, parent):
        """إنشاء تبويب المستندات"""
        # شريط الأدوات
        toolbar_frame = ttk_bs.Frame(parent)
        toolbar_frame.pack(fill=X, padx=5, pady=5)

        ttk_bs.Button(
            toolbar_frame,
            text="تحديث",
            bootstyle=SECONDARY,
            command=lambda: self.load_documents()
        ).pack(side=RIGHT)

        # جدول المستندات
        tree_frame = ttk_bs.Frame(parent)
        tree_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)

        columns = ("document_name", "document_type", "file_size", "uploaded_at")
        self.docs_tree = ttk_bs.Treeview(tree_frame, columns=columns, show="headings", height=15)

        headers = {
            "document_name": "اسم المستند",
            "document_type": "النوع",
            "file_size": "الحجم",
            "uploaded_at": "تاريخ الرفع"
        }

        for col in columns:
            self.docs_tree.heading(col, text=headers[col])
            self.docs_tree.column(col, width=150)

        scrollbar_docs = ttk_bs.Scrollbar(tree_frame, orient=VERTICAL, command=self.docs_tree.yview)
        self.docs_tree.configure(yscrollcommand=scrollbar_docs.set)

        self.docs_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar_docs.pack(side=RIGHT, fill=Y)

        self.load_documents()

    def get_status_display(self):
        """الحصول على عرض الحالة"""
        status_names = {
            'planning': 'تخطيط',
            'design': 'تصميم',
            'tender': 'ترسية',
            'execution': 'تنفيذ',
            'delivery': 'تسليم',
            'completed': 'مكتمل'
        }
        return status_names.get(self.project_data.get('status', ''), self.project_data.get('status', ''))

    def load_phases(self):
        """تحميل مراحل المشروع"""
        # مسح البيانات الحالية
        for item in self.phases_tree.get_children():
            self.phases_tree.delete(item)

        # تحميل البيانات من قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT phase_name, start_date, end_date, status, progress_percentage
            FROM project_phases
            WHERE project_id = ?
            ORDER BY start_date
        ''', (self.project_data['id'],))

        phases = cursor.fetchall()

        for phase in phases:
            status_names = {
                'pending': 'معلق',
                'in_progress': 'قيد التنفيذ',
                'completed': 'مكتمل',
                'delayed': 'متأخر'
            }
            status_display = status_names.get(phase['status'], phase['status'])
            progress = f"{phase['progress_percentage']:.1f}%" if phase['progress_percentage'] else "0%"

            self.phases_tree.insert("", "end", values=(
                phase['phase_name'],
                phase['start_date'] or "",
                phase['end_date'] or "",
                status_display,
                progress
            ))

        conn.close()

    def load_documents(self):
        """تحميل مستندات المشروع"""
        # مسح البيانات الحالية
        for item in self.docs_tree.get_children():
            self.docs_tree.delete(item)

        # تحميل البيانات من قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT document_name, document_type, file_size, uploaded_at
            FROM documents
            WHERE entity_type = 'project' AND entity_id = ?
            ORDER BY uploaded_at DESC
        ''', (self.project_data['id'],))

        documents = cursor.fetchall()

        for doc in documents:
            # تنسيق حجم الملف
            file_size = doc['file_size']
            if file_size:
                if file_size < 1024:
                    size_display = f"{file_size} بايت"
                elif file_size < 1024 * 1024:
                    size_display = f"{file_size / 1024:.1f} كيلوبايت"
                else:
                    size_display = f"{file_size / (1024 * 1024):.1f} ميجابايت"
            else:
                size_display = "غير محدد"

            # تنسيق التاريخ
            uploaded_at = doc['uploaded_at']
            if uploaded_at:
                uploaded_at = datetime.fromisoformat(uploaded_at).strftime("%Y-%m-%d %H:%M")
            else:
                uploaded_at = "غير محدد"

            self.docs_tree.insert("", "end", values=(
                doc['document_name'],
                doc['document_type'] or "غير محدد",
                size_display,
                uploaded_at
            ))

        conn.close()


class ProjectPhasesWindow:
    """نافذة إدارة مراحل المشروع"""

    def __init__(self, parent, db_manager, auth_manager, project_id, project_name):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.project_id = project_id
        self.project_name = project_name
        self.window = None
        self.phases_tree = None

    def show(self):
        """عرض نافذة إدارة المراحل"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"إدارة مراحل المشروع - {self.project_name}")
        self.window.geometry("800x600")
        self.window.resizable(True, True)

        self.create_interface()
        self.load_phases()

    def create_interface(self):
        """إنشاء واجهة إدارة المراحل"""
        # شريط الأدوات
        toolbar_frame = ttk_bs.Frame(self.window)
        toolbar_frame.pack(fill=X, padx=5, pady=5)

        ttk_bs.Button(
            toolbar_frame,
            text="مرحلة جديدة",
            bootstyle=SUCCESS,
            command=self.add_phase
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            toolbar_frame,
            text="تعديل",
            bootstyle=WARNING,
            command=self.edit_phase
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            toolbar_frame,
            text="حذف",
            bootstyle=DANGER,
            command=self.delete_phase
        ).pack(side=LEFT, padx=2)

        ttk_bs.Button(
            toolbar_frame,
            text="تحديث",
            bootstyle=SECONDARY,
            command=self.load_phases
        ).pack(side=RIGHT, padx=2)

        # جدول المراحل
        tree_frame = ttk_bs.Frame(self.window)
        tree_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)

        columns = ("id", "phase_name", "start_date", "end_date", "status", "progress", "notes")
        self.phases_tree = ttk_bs.Treeview(tree_frame, columns=columns, show="headings", height=20)

        headers = {
            "id": "الرقم",
            "phase_name": "اسم المرحلة",
            "start_date": "تاريخ البداية",
            "end_date": "تاريخ الانتهاء",
            "status": "الحالة",
            "progress": "نسبة الإنجاز",
            "notes": "ملاحظات"
        }

        for col in columns:
            self.phases_tree.heading(col, text=headers[col])
            if col == "notes":
                self.phases_tree.column(col, width=200)
            elif col == "phase_name":
                self.phases_tree.column(col, width=150)
            else:
                self.phases_tree.column(col, width=100)

        scrollbar = ttk_bs.Scrollbar(tree_frame, orient=VERTICAL, command=self.phases_tree.yview)
        self.phases_tree.configure(yscrollcommand=scrollbar.set)

        self.phases_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # ربط النقر المزدوج بالتعديل
        self.phases_tree.bind("<Double-1>", lambda e: self.edit_phase())

    def load_phases(self):
        """تحميل مراحل المشروع"""
        # مسح البيانات الحالية
        for item in self.phases_tree.get_children():
            self.phases_tree.delete(item)

        # تحميل البيانات من قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, phase_name, start_date, end_date, status, progress_percentage, notes
            FROM project_phases
            WHERE project_id = ?
            ORDER BY start_date
        ''', (self.project_id,))

        phases = cursor.fetchall()

        for phase in phases:
            status_names = {
                'pending': 'معلق',
                'in_progress': 'قيد التنفيذ',
                'completed': 'مكتمل',
                'delayed': 'متأخر'
            }
            status_display = status_names.get(phase['status'], phase['status'])
            progress = f"{phase['progress_percentage']:.1f}%" if phase['progress_percentage'] else "0%"

            self.phases_tree.insert("", "end", values=(
                phase['id'],
                phase['phase_name'],
                phase['start_date'] or "",
                phase['end_date'] or "",
                status_display,
                progress,
                phase['notes'] or ""
            ))

        conn.close()

    def add_phase(self):
        """إضافة مرحلة جديدة"""
        if not self.auth_manager.has_permission('engineer'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإضافة المراحل")
            return

        dialog = PhaseDialog(self.window, self.db_manager, self.project_id, "إضافة مرحلة جديدة")
        if dialog.show():
            self.load_phases()

    def edit_phase(self):
        """تعديل مرحلة"""
        selected = self.phases_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مرحلة للتعديل")
            return

        if not self.auth_manager.has_permission('engineer'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لتعديل المراحل")
            return

        phase_id = self.phases_tree.item(selected[0])['values'][0]

        # تحميل بيانات المرحلة
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM project_phases WHERE id = ?", (phase_id,))
        phase_data = cursor.fetchone()
        conn.close()

        if phase_data:
            dialog = PhaseDialog(self.window, self.db_manager, self.project_id, "تعديل المرحلة", dict(phase_data))
            if dialog.show():
                self.load_phases()

    def delete_phase(self):
        """حذف مرحلة"""
        selected = self.phases_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مرحلة للحذف")
            return

        if not self.auth_manager.has_permission('manager'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لحذف المراحل")
            return

        phase_id = self.phases_tree.item(selected[0])['values'][0]
        phase_name = self.phases_tree.item(selected[0])['values'][1]

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المرحلة '{phase_name}'؟"):
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            try:
                cursor.execute("DELETE FROM project_phases WHERE id = ?", (phase_id,))
                conn.commit()
                messagebox.showinfo("نجح", "تم حذف المرحلة بنجاح")
                self.load_phases()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المرحلة: {str(e)}")
            finally:
                conn.close()


class PhaseDialog:
    """نافذة إضافة/تعديل مرحلة المشروع"""

    def __init__(self, parent, db_manager, project_id, title, phase_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.project_id = project_id
        self.title = title
        self.phase_data = phase_data
        self.result = False
        self.window = None

    def show(self):
        """عرض النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("500x600")
        self.window.resizable(False, False)
        self.window.transient(self.parent)
        self.window.grab_set()

        self.create_form()

        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")

        self.window.wait_window()
        return self.result

    def create_form(self):
        """إنشاء نموذج المرحلة"""
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # اسم المرحلة
        ttk_bs.Label(main_frame, text="اسم المرحلة: *").pack(anchor=W, pady=(0, 5))
        self.phase_name_entry = ttk_bs.Entry(main_frame, width=50)
        self.phase_name_entry.pack(pady=(0, 10))

        # وصف المرحلة
        ttk_bs.Label(main_frame, text="وصف المرحلة:").pack(anchor=W, pady=(0, 5))
        self.description_text = tk.Text(main_frame, height=4, width=50)
        self.description_text.pack(pady=(0, 10))

        # التواريخ
        dates_frame = ttk_bs.Frame(main_frame)
        dates_frame.pack(fill=X, pady=10)

        # تاريخ البداية
        start_frame = ttk_bs.Frame(dates_frame)
        start_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 5))
        ttk_bs.Label(start_frame, text="تاريخ البداية:").pack(anchor=W)
        self.start_date_entry = ttk_bs.DateEntry(start_frame, dateformat="%Y-%m-%d")
        self.start_date_entry.pack(fill=X)

        # تاريخ الانتهاء
        end_frame = ttk_bs.Frame(dates_frame)
        end_frame.pack(side=LEFT, fill=X, expand=True, padx=(5, 0))
        ttk_bs.Label(end_frame, text="تاريخ الانتهاء:").pack(anchor=W)
        self.end_date_entry = ttk_bs.DateEntry(end_frame, dateformat="%Y-%m-%d")
        self.end_date_entry.pack(fill=X)

        # الحالة
        ttk_bs.Label(main_frame, text="حالة المرحلة:").pack(anchor=W, pady=(10, 5))
        self.status_combo = ttk.Combobox(main_frame, width=47, state="readonly")
        self.status_combo['values'] = ("pending", "in_progress", "completed", "delayed")
        self.status_combo.set("pending")
        self.status_combo.pack(pady=(0, 10))

        # نسبة الإنجاز
        ttk_bs.Label(main_frame, text="نسبة الإنجاز (%):").pack(anchor=W, pady=(0, 5))
        self.progress_var = tk.DoubleVar(value=0)
        self.progress_scale = ttk_bs.Scale(
            main_frame,
            from_=0,
            to=100,
            orient=HORIZONTAL,
            variable=self.progress_var,
            length=400
        )
        self.progress_scale.pack(pady=(0, 5))

        self.progress_label = ttk_bs.Label(main_frame, text="0%")
        self.progress_label.pack(pady=(0, 10))

        # ربط تحديث النسبة
        self.progress_var.trace('w', self.update_progress_label)

        # ملاحظات
        ttk_bs.Label(main_frame, text="ملاحظات:").pack(anchor=W, pady=(0, 5))
        self.notes_text = tk.Text(main_frame, height=4, width=50)
        self.notes_text.pack(pady=(0, 10))

        # ملء البيانات إذا كان تعديل
        if self.phase_data:
            self.phase_name_entry.insert(0, self.phase_data.get('phase_name', ''))
            self.description_text.insert('1.0', self.phase_data.get('description', '') or '')

            if self.phase_data.get('start_date'):
                self.start_date_entry.entry.delete(0, tk.END)
                self.start_date_entry.entry.insert(0, self.phase_data['start_date'])

            if self.phase_data.get('end_date'):
                self.end_date_entry.entry.delete(0, tk.END)
                self.end_date_entry.entry.insert(0, self.phase_data['end_date'])

            self.status_combo.set(self.phase_data.get('status', 'pending'))
            self.progress_var.set(self.phase_data.get('progress_percentage', 0))
            self.notes_text.insert('1.0', self.phase_data.get('notes', '') or '')

        # الأزرار
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.pack(fill=X, pady=20)

        ttk_bs.Button(
            button_frame,
            text="حفظ",
            bootstyle=SUCCESS,
            command=self.save_phase
        ).pack(side=RIGHT, padx=(5, 0))

        ttk_bs.Button(
            button_frame,
            text="إلغاء",
            bootstyle=SECONDARY,
            command=self.window.destroy
        ).pack(side=RIGHT)

    def update_progress_label(self, *args):
        """تحديث تسمية نسبة الإنجاز"""
        progress = self.progress_var.get()
        self.progress_label.config(text=f"{progress:.1f}%")

    def save_phase(self):
        """حفظ بيانات المرحلة"""
        # التحقق من صحة البيانات
        phase_name = self.phase_name_entry.get().strip()
        description = self.description_text.get('1.0', tk.END).strip()
        start_date = self.start_date_entry.entry.get()
        end_date = self.end_date_entry.entry.get()
        status = self.status_combo.get()
        progress = self.progress_var.get()
        notes = self.notes_text.get('1.0', tk.END).strip()

        if not phase_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المرحلة")
            return

        # حفظ في قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if self.phase_data:  # تعديل
                cursor.execute('''
                    UPDATE project_phases
                    SET phase_name=?, description=?, start_date=?, end_date=?,
                        status=?, progress_percentage=?, notes=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                ''', (phase_name, description or None, start_date or None,
                      end_date or None, status, progress, notes or None,
                      self.phase_data['id']))
            else:  # إضافة جديد
                cursor.execute('''
                    INSERT INTO project_phases (project_id, phase_name, description,
                                              start_date, end_date, status,
                                              progress_percentage, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (self.project_id, phase_name, description or None,
                      start_date or None, end_date or None, status, progress,
                      notes or None))

            conn.commit()
            messagebox.showinfo("نجح", "تم حفظ بيانات المرحلة بنجاح")
            self.result = True
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
        finally:
            conn.close()