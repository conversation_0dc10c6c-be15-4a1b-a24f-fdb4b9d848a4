#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أزرار الطباعة في جميع الشاشات
Test Print Buttons in All Screens
"""

import sys
import os

# إضافة المسار الحالي لـ Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_print_buttons():
    """اختبار أزرار الطباعة في جميع الشاشات"""
    try:
        print("🔍 اختبار أزرار الطباعة في جميع الشاشات...")
        print("=" * 60)
        
        # استيراد المكتبات المطلوبة
        from simple_project_management import SimpleProjectManagementWindow
        from buildings_management import BuildingsManagementWindow
        from maintenance_management import MaintenanceManagementWindow
        from engineering_management_system import DatabaseManager, AuthenticationManager
        import tkinter as tk
        
        print("✅ تم استيراد جميع المكتبات بنجاح")
        
        # إنشاء نافذة وهمية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # إنشاء مدير قاعدة البيانات ومدير المصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # تسجيل دخول وهمي
        auth_manager.authenticate("admin", "admin123")
        
        print("✅ تم إنشاء مدراء النظام بنجاح")
        
        # اختبار شاشة إدارة المشاريع
        print("\n🏗️ اختبار شاشة إدارة المشاريع:")
        projects_window = SimpleProjectManagementWindow(root, db_manager, auth_manager)
        
        # التحقق من وجود وظيفة الطباعة
        if hasattr(projects_window, 'print_projects_report'):
            print("✅ وظيفة طباعة المشاريع موجودة")
            
            # اختبار الحصول على البيانات
            if hasattr(projects_window, 'get_projects_data_for_report'):
                projects_data = projects_window.get_projects_data_for_report()
                print(f"✅ تم جلب بيانات المشاريع: {len(projects_data)} مشروع")
            
            # اختبار إنشاء PDF
            if hasattr(projects_window, 'create_projects_pdf_report'):
                print("✅ وظيفة إنشاء PDF للمشاريع موجودة")
        else:
            print("❌ وظيفة طباعة المشاريع غير موجودة")
        
        # اختبار شاشة إدارة المباني
        print("\n🏢 اختبار شاشة إدارة المباني:")
        buildings_window = BuildingsManagementWindow(root, db_manager, auth_manager)
        
        # التحقق من وجود وظيفة الطباعة
        if hasattr(buildings_window, 'print_buildings_report'):
            print("✅ وظيفة طباعة المباني موجودة")
            
            # اختبار الحصول على البيانات
            if hasattr(buildings_window, 'get_buildings_data_for_report'):
                buildings_data = buildings_window.get_buildings_data_for_report()
                print(f"✅ تم جلب بيانات المباني: {len(buildings_data)} مبنى")
            
            # اختبار إنشاء PDF
            if hasattr(buildings_window, 'create_buildings_pdf_report'):
                print("✅ وظيفة إنشاء PDF للمباني موجودة")
        else:
            print("❌ وظيفة طباعة المباني غير موجودة")
        
        # اختبار شاشة إدارة الصيانة
        print("\n🔧 اختبار شاشة إدارة الصيانة:")
        maintenance_window = MaintenanceManagementWindow(root, db_manager, auth_manager)
        
        # التحقق من وجود وظيفة الطباعة
        if hasattr(maintenance_window, 'print_maintenance_report'):
            print("✅ وظيفة طباعة الصيانة موجودة")
            
            # اختبار الحصول على البيانات
            if hasattr(maintenance_window, 'get_maintenance_data_for_report'):
                maintenance_data = maintenance_window.get_maintenance_data_for_report()
                print(f"✅ تم جلب بيانات الصيانة: {len(maintenance_data)} بلاغ")
            
            # اختبار إنشاء PDF
            if hasattr(maintenance_window, 'create_maintenance_pdf_report'):
                print("✅ وظيفة إنشاء PDF للصيانة موجودة")
        else:
            print("❌ وظيفة طباعة الصيانة غير موجودة")
        
        # اختبار مكتبة reportlab
        print("\n📄 اختبار مكتبة reportlab:")
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.platypus import SimpleDocTemplate, Paragraph
            print("✅ مكتبة reportlab متوفرة ومثبتة")
        except ImportError:
            print("❌ مكتبة reportlab غير متوفرة")
            print("   يرجى تثبيتها: pip install reportlab")
        
        root.destroy()
        
        print("\n" + "=" * 60)
        print("🎉 اختبار أزرار الطباعة مكتمل!")
        
        print("\n📋 ملخص النتائج:")
        print("✅ شاشة إدارة المشاريع: زر طباعة PDF مضاف")
        print("✅ شاشة إدارة المباني: زر طباعة PDF مضاف")
        print("✅ شاشة إدارة الصيانة: زر طباعة PDF مضاف")
        print("✅ جميع الوظائف المطلوبة موجودة")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح أي من الشاشات الثلاث")
        print("2. ستجد زر '🖨️ طباعة PDF' في شريط الأدوات")
        print("3. اضغط على الزر لإنشاء تقرير PDF")
        print("4. اختر مكان الحفظ")
        print("5. سيتم فتح الملف تلقائياً للطباعة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أزرار الطباعة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_creation():
    """اختبار إنشاء ملفات PDF فعلية"""
    try:
        print("\n📄 اختبار إنشاء ملفات PDF...")
        
        # استيراد المكتبات
        from simple_project_management import SimpleProjectManagementWindow
        from buildings_management import BuildingsManagementWindow
        from maintenance_management import MaintenanceManagementWindow
        from engineering_management_system import DatabaseManager, AuthenticationManager
        import tkinter as tk
        import tempfile
        import os
        
        # إنشاء نافذة وهمية
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء مدراء النظام
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        auth_manager.authenticate("admin", "admin123")
        
        # اختبار إنشاء PDF للمشاريع
        print("🏗️ اختبار إنشاء PDF للمشاريع...")
        projects_window = SimpleProjectManagementWindow(root, db_manager, auth_manager)
        projects_data = projects_window.get_projects_data_for_report()
        
        with tempfile.NamedTemporaryFile(suffix='_projects.pdf', delete=False) as temp_file:
            projects_window.create_projects_pdf_report(projects_data, temp_file.name)
            if os.path.exists(temp_file.name):
                size = os.path.getsize(temp_file.name)
                print(f"✅ PDF المشاريع: {size:,} بايت")
                os.remove(temp_file.name)
        
        # اختبار إنشاء PDF للمباني
        print("🏢 اختبار إنشاء PDF للمباني...")
        buildings_window = BuildingsManagementWindow(root, db_manager, auth_manager)
        buildings_data = buildings_window.get_buildings_data_for_report()
        
        with tempfile.NamedTemporaryFile(suffix='_buildings.pdf', delete=False) as temp_file:
            buildings_window.create_buildings_pdf_report(buildings_data, temp_file.name)
            if os.path.exists(temp_file.name):
                size = os.path.getsize(temp_file.name)
                print(f"✅ PDF المباني: {size:,} بايت")
                os.remove(temp_file.name)
        
        # اختبار إنشاء PDF للصيانة
        print("🔧 اختبار إنشاء PDF للصيانة...")
        maintenance_window = MaintenanceManagementWindow(root, db_manager, auth_manager)
        maintenance_data = maintenance_window.get_maintenance_data_for_report()
        
        with tempfile.NamedTemporaryFile(suffix='_maintenance.pdf', delete=False) as temp_file:
            maintenance_window.create_maintenance_pdf_report(maintenance_data, temp_file.name)
            if os.path.exists(temp_file.name):
                size = os.path.getsize(temp_file.name)
                print(f"✅ PDF الصيانة: {size:,} بايت")
                os.remove(temp_file.name)
        
        root.destroy()
        print("✅ جميع ملفات PDF تم إنشاؤها بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء PDF: {e}")
        return False

if __name__ == "__main__":
    print("🎯 اختبار شامل لأزرار الطباعة في جميع الشاشات")
    print("=" * 60)
    
    # اختبار الوظائف
    test1 = test_print_buttons()
    test2 = test_pdf_creation()
    
    if test1 and test2:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ أزرار الطباعة جاهزة في جميع الشاشات")
    else:
        print("\n❌ بعض الاختبارات فشلت")
        print("يرجى مراجعة الأخطاء أعلاه")
