#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لحفظ المباني
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from engineering_management_system import DatabaseManager, AuthenticationManager
from buildings_management import BuildingDialog

def test_building_save():
    """اختبار حفظ مبنى جديد"""
    try:
        # إنشاء قاعدة البيانات ومدير المصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # تسجيل دخول تجريبي
        auth_manager.authenticate("admin", "admin123")
        
        # إنشاء نافذة مؤقتة
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # إنشاء نافذة إضافة مبنى
        dialog = BuildingDialog(root, db_manager, auth_manager, "اختبار إضافة مبنى")
        
        print("🏢 فتح نافذة إضافة مبنى...")
        print("📝 يرجى ملء البيانات والضغط على حفظ لاختبار العملية")
        
        # عرض النافذة
        result = dialog.show()
        
        if result:
            print("✅ تم حفظ المبنى بنجاح!")
            messagebox.showinfo("نجح", "✅ تم حفظ المبنى بنجاح!")
        else:
            print("❌ تم إلغاء العملية")
            
        root.destroy()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        messagebox.showerror("خطأ", f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    test_building_save()
