#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نوافذ محسنة باستخدام ttkbootstrap
Enhanced Dialogs using ttkbootstrap
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import datetime

class ProjectDialog:
    """نافذة إضافة/تعديل مشروع"""
    
    def __init__(self, parent, title="إدارة المشروع"):
        self.parent = parent
        self.title = title
        self.window = None
        self.result = None
        
    def show(self):
        """عرض النافذة"""
        self.window = ttk_bs.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("600x500")
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # انتظار إغلاق النافذة
        self.window.wait_window()
        
        return self.result
    
    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="🏗️ " + self.title,
            font=("Segoe UI", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))
        
        # إطار البيانات
        data_frame = ttk_bs.LabelFrame(
            main_frame,
            text="📋 بيانات المشروع",
            padding=20,
            bootstyle="info"
        )
        data_frame.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        # اسم المشروع
        ttk_bs.Label(
            data_frame,
            text="📝 اسم المشروع:",
            font=("Segoe UI", 11)
        ).grid(row=0, column=0, sticky=W, pady=5)
        
        self.project_name_var = tk.StringVar()
        ttk_bs.Entry(
            data_frame,
            textvariable=self.project_name_var,
            font=("Segoe UI", 11),
            width=40
        ).grid(row=0, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # وصف المشروع
        ttk_bs.Label(
            data_frame,
            text="📄 وصف المشروع:",
            font=("Segoe UI", 11)
        ).grid(row=1, column=0, sticky=W+N, pady=5)
        
        self.description_text = ttk_bs.Text(
            data_frame,
            height=4,
            width=40,
            font=("Segoe UI", 10)
        )
        self.description_text.grid(row=1, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # تاريخ البداية
        ttk_bs.Label(
            data_frame,
            text="📅 تاريخ البداية:",
            font=("Segoe UI", 11)
        ).grid(row=2, column=0, sticky=W, pady=5)
        
        self.start_date_var = tk.StringVar(value=datetime.date.today().strftime("%Y-%m-%d"))
        ttk_bs.Entry(
            data_frame,
            textvariable=self.start_date_var,
            font=("Segoe UI", 11),
            width=40
        ).grid(row=2, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # تاريخ النهاية المتوقعة
        ttk_bs.Label(
            data_frame,
            text="📅 تاريخ النهاية المتوقعة:",
            font=("Segoe UI", 11)
        ).grid(row=3, column=0, sticky=W, pady=5)
        
        self.end_date_var = tk.StringVar()
        ttk_bs.Entry(
            data_frame,
            textvariable=self.end_date_var,
            font=("Segoe UI", 11),
            width=40
        ).grid(row=3, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الميزانية
        ttk_bs.Label(
            data_frame,
            text="💰 الميزانية (ريال):",
            font=("Segoe UI", 11)
        ).grid(row=4, column=0, sticky=W, pady=5)
        
        self.budget_var = tk.StringVar()
        ttk_bs.Entry(
            data_frame,
            textvariable=self.budget_var,
            font=("Segoe UI", 11),
            width=40
        ).grid(row=4, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # حالة المشروع
        ttk_bs.Label(
            data_frame,
            text="📊 حالة المشروع:",
            font=("Segoe UI", 11)
        ).grid(row=5, column=0, sticky=W, pady=5)
        
        self.status_var = tk.StringVar(value="مخطط")
        status_combo = ttk_bs.Combobox(
            data_frame,
            textvariable=self.status_var,
            values=["مخطط", "قيد التنفيذ", "متوقف", "مكتمل", "ملغي"],
            state="readonly",
            font=("Segoe UI", 11),
            width=37
        )
        status_combo.grid(row=5, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # تكوين الشبكة
        data_frame.columnconfigure(1, weight=1)
        
        # أزرار
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(fill=X)
        
        ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_project,
            bootstyle="success",
            width=15
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            bootstyle="secondary",
            width=15
        ).pack(side=LEFT)
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 600
        height = 500
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def save_project(self):
        """حفظ المشروع"""
        # التحقق من البيانات
        if not self.project_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
            return
        
        # جمع البيانات
        self.result = {
            'name': self.project_name_var.get().strip(),
            'description': self.description_text.get("1.0", "end-1c").strip(),
            'start_date': self.start_date_var.get(),
            'end_date': self.end_date_var.get(),
            'budget': self.budget_var.get(),
            'status': self.status_var.get()
        }
        
        messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح!")
        self.window.destroy()
    
    def cancel(self):
        """إلغاء"""
        self.window.destroy()

class BuildingDialog:
    """نافذة إضافة/تعديل مبنى"""
    
    def __init__(self, parent, title="إدارة المبنى"):
        self.parent = parent
        self.title = title
        self.window = None
        self.result = None
        
    def show(self):
        """عرض النافذة"""
        self.window = ttk_bs.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("550x450")
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # انتظار إغلاق النافذة
        self.window.wait_window()
        
        return self.result
    
    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="🏢 " + self.title,
            font=("Segoe UI", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))
        
        # إطار البيانات
        data_frame = ttk_bs.LabelFrame(
            main_frame,
            text="🏗️ بيانات المبنى",
            padding=20,
            bootstyle="info"
        )
        data_frame.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        # اسم المبنى
        ttk_bs.Label(
            data_frame,
            text="🏢 اسم المبنى:",
            font=("Segoe UI", 11)
        ).grid(row=0, column=0, sticky=W, pady=5)
        
        self.building_name_var = tk.StringVar()
        ttk_bs.Entry(
            data_frame,
            textvariable=self.building_name_var,
            font=("Segoe UI", 11),
            width=35
        ).grid(row=0, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # نوع المبنى
        ttk_bs.Label(
            data_frame,
            text="🏗️ نوع المبنى:",
            font=("Segoe UI", 11)
        ).grid(row=1, column=0, sticky=W, pady=5)
        
        self.building_type_var = tk.StringVar(value="إداري")
        type_combo = ttk_bs.Combobox(
            data_frame,
            textvariable=self.building_type_var,
            values=["إداري", "سكني", "تجاري", "صناعي", "تعليمي", "صحي", "ترفيهي"],
            state="readonly",
            font=("Segoe UI", 11),
            width=32
        )
        type_combo.grid(row=1, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الموقع
        ttk_bs.Label(
            data_frame,
            text="📍 الموقع:",
            font=("Segoe UI", 11)
        ).grid(row=2, column=0, sticky=W, pady=5)
        
        self.location_var = tk.StringVar()
        ttk_bs.Entry(
            data_frame,
            textvariable=self.location_var,
            font=("Segoe UI", 11),
            width=35
        ).grid(row=2, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # عدد الطوابق
        ttk_bs.Label(
            data_frame,
            text="🏗️ عدد الطوابق:",
            font=("Segoe UI", 11)
        ).grid(row=3, column=0, sticky=W, pady=5)
        
        self.floors_var = tk.StringVar(value="1")
        ttk_bs.Entry(
            data_frame,
            textvariable=self.floors_var,
            font=("Segoe UI", 11),
            width=35
        ).grid(row=3, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # المساحة
        ttk_bs.Label(
            data_frame,
            text="📐 المساحة (م²):",
            font=("Segoe UI", 11)
        ).grid(row=4, column=0, sticky=W, pady=5)
        
        self.area_var = tk.StringVar()
        ttk_bs.Entry(
            data_frame,
            textvariable=self.area_var,
            font=("Segoe UI", 11),
            width=35
        ).grid(row=4, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # تاريخ الإنشاء
        ttk_bs.Label(
            data_frame,
            text="📅 تاريخ الإنشاء:",
            font=("Segoe UI", 11)
        ).grid(row=5, column=0, sticky=W, pady=5)
        
        self.construction_date_var = tk.StringVar()
        ttk_bs.Entry(
            data_frame,
            textvariable=self.construction_date_var,
            font=("Segoe UI", 11),
            width=35
        ).grid(row=5, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الحالة
        ttk_bs.Label(
            data_frame,
            text="📊 الحالة:",
            font=("Segoe UI", 11)
        ).grid(row=6, column=0, sticky=W, pady=5)
        
        self.status_var = tk.StringVar(value="جيد")
        status_combo = ttk_bs.Combobox(
            data_frame,
            textvariable=self.status_var,
            values=["ممتاز", "جيد", "متوسط", "يحتاج صيانة", "سيء"],
            state="readonly",
            font=("Segoe UI", 11),
            width=32
        )
        status_combo.grid(row=6, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # تكوين الشبكة
        data_frame.columnconfigure(1, weight=1)
        
        # أزرار
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(fill=X)
        
        ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_building,
            bootstyle="success",
            width=15
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            bootstyle="secondary",
            width=15
        ).pack(side=LEFT)
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 550
        height = 450
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def save_building(self):
        """حفظ المبنى"""
        # التحقق من البيانات
        if not self.building_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المبنى")
            return
        
        # جمع البيانات
        self.result = {
            'name': self.building_name_var.get().strip(),
            'type': self.building_type_var.get(),
            'location': self.location_var.get().strip(),
            'floors': self.floors_var.get(),
            'area': self.area_var.get(),
            'construction_date': self.construction_date_var.get(),
            'status': self.status_var.get()
        }
        
        messagebox.showinfo("نجح", "تم حفظ المبنى بنجاح!")
        self.window.destroy()
    
    def cancel(self):
        """إلغاء"""
        self.window.destroy()

class MaintenanceDialog:
    """نافذة إضافة/تعديل بلاغ صيانة"""
    
    def __init__(self, parent, title="بلاغ صيانة"):
        self.parent = parent
        self.title = title
        self.window = None
        self.result = None
        
    def show(self):
        """عرض النافذة"""
        self.window = ttk_bs.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # انتظار إغلاق النافذة
        self.window.wait_window()
        
        return self.result
    
    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="🔧 " + self.title,
            font=("Segoe UI", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))
        
        # إطار البيانات
        data_frame = ttk_bs.LabelFrame(
            main_frame,
            text="📋 بيانات البلاغ",
            padding=20,
            bootstyle="warning"
        )
        data_frame.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        # عنوان البلاغ
        ttk_bs.Label(
            data_frame,
            text="📝 عنوان البلاغ:",
            font=("Segoe UI", 11)
        ).grid(row=0, column=0, sticky=W, pady=5)
        
        self.title_var = tk.StringVar()
        ttk_bs.Entry(
            data_frame,
            textvariable=self.title_var,
            font=("Segoe UI", 11),
            width=30
        ).grid(row=0, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # نوع البلاغ
        ttk_bs.Label(
            data_frame,
            text="🔧 نوع البلاغ:",
            font=("Segoe UI", 11)
        ).grid(row=1, column=0, sticky=W, pady=5)
        
        self.type_var = tk.StringVar(value="صيانة عامة")
        type_combo = ttk_bs.Combobox(
            data_frame,
            textvariable=self.type_var,
            values=["صيانة عامة", "كهرباء", "سباكة", "تكييف", "نظافة", "أمن وسلامة"],
            state="readonly",
            font=("Segoe UI", 11),
            width=27
        )
        type_combo.grid(row=1, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الأولوية
        ttk_bs.Label(
            data_frame,
            text="⚡ الأولوية:",
            font=("Segoe UI", 11)
        ).grid(row=2, column=0, sticky=W, pady=5)
        
        self.priority_var = tk.StringVar(value="متوسطة")
        priority_combo = ttk_bs.Combobox(
            data_frame,
            textvariable=self.priority_var,
            values=["منخفضة", "متوسطة", "عالية", "عاجلة"],
            state="readonly",
            font=("Segoe UI", 11),
            width=27
        )
        priority_combo.grid(row=2, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # الوصف
        ttk_bs.Label(
            data_frame,
            text="📄 الوصف:",
            font=("Segoe UI", 11)
        ).grid(row=3, column=0, sticky=W+N, pady=5)
        
        self.description_text = ttk_bs.Text(
            data_frame,
            height=6,
            width=30,
            font=("Segoe UI", 10)
        )
        self.description_text.grid(row=3, column=1, sticky=W+E, pady=5, padx=(10, 0))
        
        # تكوين الشبكة
        data_frame.columnconfigure(1, weight=1)
        
        # أزرار
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(fill=X)
        
        ttk_bs.Button(
            buttons_frame,
            text="📤 إرسال البلاغ",
            command=self.save_maintenance,
            bootstyle="warning",
            width=15
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            bootstyle="secondary",
            width=15
        ).pack(side=LEFT)
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 500
        height = 400
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def save_maintenance(self):
        """حفظ بلاغ الصيانة"""
        # التحقق من البيانات
        if not self.title_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال عنوان البلاغ")
            return
        
        # جمع البيانات
        self.result = {
            'title': self.title_var.get().strip(),
            'type': self.type_var.get(),
            'priority': self.priority_var.get(),
            'description': self.description_text.get("1.0", "end-1c").strip(),
            'date': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        messagebox.showinfo("نجح", "تم إرسال البلاغ بنجاح!")
        self.window.destroy()
    
    def cancel(self):
        """إلغاء"""
        self.window.destroy()

# دالة اختبار النوافذ
def test_dialogs():
    """اختبار النوافذ المحسنة"""
    root = ttk_bs.Window(
        title="اختبار النوافذ المحسنة",
        themename="cosmo",
        size=(400, 300)
    )
    
    def test_project():
        dialog = ProjectDialog(root, "إضافة مشروع جديد")
        result = dialog.show()
        if result:
            print("بيانات المشروع:", result)
    
    def test_building():
        dialog = BuildingDialog(root, "إضافة مبنى جديد")
        result = dialog.show()
        if result:
            print("بيانات المبنى:", result)
    
    def test_maintenance():
        dialog = MaintenanceDialog(root, "بلاغ صيانة جديد")
        result = dialog.show()
        if result:
            print("بيانات البلاغ:", result)
    
    # أزرار الاختبار
    ttk_bs.Button(root, text="اختبار نافذة المشروع", command=test_project).pack(pady=10)
    ttk_bs.Button(root, text="اختبار نافذة المبنى", command=test_building).pack(pady=10)
    ttk_bs.Button(root, text="اختبار نافذة الصيانة", command=test_maintenance).pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    test_dialogs()