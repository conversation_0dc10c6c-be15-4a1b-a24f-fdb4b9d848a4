# 🔧 إصلاح خطأ حفظ بلاغات الصيانة الجديدة

## 🚨 المشكلة
كان يظهر خطأ "CHECK constraint failed" عند محاولة حفظ بلاغ صيانة جديد بسبب:

1. **مشكلة في معرف المبنى**: الكود كان يرسل نص مثل "المبنى الإداري" بدلاً من رقم صحيح لحقل `building_id`
2. **مشكلة في معرف المكلف**: الكود كان يرسل نص بدلاً من رقم صحيح لحقل `assigned_to`
3. **مشكلة في قيود قاعدة البيانات**: الكود كان يرسل قيم عربية بينما قاعدة البيانات تتطلب قيم إنجليزية محددة

## 🔍 تحليل المشكلة

### هيكل الجدول في قاعدة البيانات:
```sql
CREATE TABLE maintenance_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    building_id INTEGER,        -- ⚠️ يتطلب رقم صحيح
    asset_id INTEGER,
    title TEXT NOT NULL,
    description TEXT,
    priority TEXT DEFAULT 'medium',
    status TEXT DEFAULT 'open',
    reported_by TEXT,
    assigned_to INTEGER,        -- ⚠️ يتطلب رقم صحيح
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_at TIMESTAMP,
    completed_at TIMESTAMP,
    notes TEXT
)
```

### قيود CHECK في قاعدة البيانات:
```sql
priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent'))
status TEXT DEFAULT 'open' CHECK (status IN ('open', 'assigned', 'in_progress', 'completed', 'closed'))
```

### المشاكل في الكود:
1. **قائمة المباني**: كانت تحتوي على أسماء نصية فقط
2. **معالجة البيانات**: لم يكن هناك تحويل للنصوص إلى أرقام
3. **قيم الأولوية والحالة**: كان الكود يرسل قيم عربية مثل "عالية" و "جديد" بينما قاعدة البيانات تتطلب قيم إنجليزية

## ✅ الحلول المطبقة

### 1. تحديث قائمة المباني
```python
# قبل الإصلاح
combo['values'] = ("المبنى الإداري", "مبنى الهندسة", "مبنى المختبرات", "مبنى الصيانة", "مبنى المؤتمرات")

# بعد الإصلاح
combo['values'] = ("1 - المبنى الإداري", "2 - مبنى الهندسة", "3 - مبنى المختبرات", "4 - مبنى الصيانة", "5 - مبنى المؤتمرات")
```

### 2. إضافة معالجة لتحويل معرف المبنى
```python
# تحويل معرف المبنى إلى رقم أو NULL
building_id = None
if building and building != "غير محدد":
    try:
        # استخراج الرقم من النص مثل "1 - المبنى الإداري"
        if " - " in building:
            building_id = int(building.split(" - ")[0])
        else:
            # محاولة استخراج أي رقم من النص
            import re
            match = re.search(r'\d+', building)
            if match:
                building_id = int(match.group())
            else:
                building_id = 1  # قيمة افتراضية
    except:
        building_id = 1  # قيمة افتراضية في حالة الخطأ
```

### 3. إضافة معالجة لتحويل معرف المكلف
```python
# تحويل assigned_to إلى رقم أو NULL
assigned_to_id = None
if assigned_to and assigned_to.strip():
    try:
        assigned_to_id = int(assigned_to) if assigned_to.isdigit() else None
    except:
        assigned_to_id = None
```

### 4. إضافة نظام تحويل القيم بين العربية والإنجليزية
```python
# خرائط التحويل في الكلاس
self.priority_ar_to_en = {
    "عالية": "urgent",
    "متوسطة": "medium",
    "منخفضة": "low"
}
self.status_ar_to_en = {
    "جديد": "open",
    "قيد التنفيذ": "in_progress",
    "مكتمل": "completed",
    "مؤجل": "assigned"
}

# تحويل القيم قبل الحفظ
priority_en = self.priority_ar_to_en.get(priority, "medium")
status_en = self.status_ar_to_en.get(status, "open")
```

### 5. تحديث استعلامات قاعدة البيانات
```python
# استخدام القيم المحولة بدلاً من النصوص الخام
cursor.execute('''
    INSERT INTO maintenance_requests (title, description, building_id, priority,
                                    status, reported_by, assigned_to, notes)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
''', (title, description, building_id, priority_en, status_en,
      self.auth_manager.current_user.get('username', 'مجهول'), assigned_to_id, notes))
```

## 🧪 الاختبار
- تم اختبار الإصلاح باستخدام `test_maintenance_save.py`
- تم التأكد من عمل الحفظ بنجاح دون أخطاء

## 📁 الملفات المعدلة
- `maintenance_management.py` - إصلاح دالة الحفظ ومعالجة البيانات

## ✨ النتيجة
- ✅ حفظ بلاغات الصيانة الجديدة يعمل بنجاح
- ✅ معالجة صحيحة لمعرفات المباني والمكلفين
- ✅ تحويل تلقائي بين القيم العربية والإنجليزية
- ✅ احترام قيود CHECK في قاعدة البيانات
- ✅ قيم افتراضية آمنة في حالة الأخطاء
- ✅ واجهة مستخدم محسنة مع قائمة مباني واضحة
- ✅ تعديل البلاغات يعمل مع التحويل الصحيح للقيم

🎉 **تم حل جميع مشاكل CHECK constraint بنجاح!**
