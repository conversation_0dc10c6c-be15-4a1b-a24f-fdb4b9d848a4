#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التذييل المحسن للصفحة الرئيسية
Test Enhanced Footer for Main Page
"""

import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import datetime

class FooterTestWindow:
    """نافذة اختبار التذييل المحسن"""
    
    def __init__(self):
        self.setup_window()
        self.create_footer()
        self.update_footer()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = ttk_bs.Window(
            title="🧪 اختبار التذييل المحسن - نظام إدارة أعمال الإدارة الهندسية",
            themename="cosmo",
            size=(1000, 600)
        )
        
        # توسيط النافذة
        self.center_window()
        
        # إطار رئيسي للمحتوى
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # عنوان الاختبار
        title_label = ttk_bs.Label(
            main_frame,
            text="🧪 اختبار التذييل المحسن",
            font=("Segoe UI", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=20)
        
        # وصف الاختبار
        desc_label = ttk_bs.Label(
            main_frame,
            text="هذا اختبار لعرض التذييل المحسن مع:\n• اسم المستخدم في اليسار\n• اسم البرنامج مع حقوق النشر © 2025 في الوسط\n• التاريخ والوقت في اليمين\n• خط أزرق غامق وثقيل",
            font=("Segoe UI", 12),
            bootstyle="info",
            justify="center"
        )
        desc_label.pack(pady=20)
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 1000
        height = 600
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_footer(self):
        """إنشاء تذييل الشاشة الرئيسية المحسن"""
        # إطار التذييل مع تدرج لوني جميل
        footer_frame = ttk_bs.Frame(self.window)
        footer_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # إطار فرعي للمحتوى مع خلفية ملونة
        content_frame = ttk_bs.Frame(footer_frame)
        content_frame.pack(fill=tk.X, padx=15, pady=12)
        
        # اسم المستخدم (يسار) مع أيقونة
        self.user_label = ttk_bs.Label(
            content_frame,
            text="👤 المستخدم: مدير النظام",
            font=("Segoe UI", 10, "bold"),  # خط ثقيل قليلاً
            foreground="#1e3a8a",  # أزرق غامق
            bootstyle="primary"
        )
        self.user_label.pack(side=tk.LEFT)
        
        # اسم البرنامج مع حقوق النشر (وسط) مع تصميم مميز
        program_label = ttk_bs.Label(
            content_frame,
            text="🏗️ نظام إدارة أعمال الإدارة الهندسية © 2025 🏗️",
            font=("Segoe UI", 10, "bold"),  # خط ثقيل قليلاً
            foreground="#1e3a8a",  # أزرق غامق
            bootstyle="primary"
        )
        program_label.pack(expand=True)
        
        # الوقت والتاريخ (يمين) مع أيقونة
        self.time_label = ttk_bs.Label(
            content_frame,
            text="",
            font=("Segoe UI", 10, "bold"),  # خط ثقيل قليلاً
            foreground="#1e3a8a",  # أزرق غامق
            bootstyle="primary"
        )
        self.time_label.pack(side=tk.RIGHT)
        
        # إضافة خط فاصل علوي للتذييل
        separator = ttk_bs.Separator(self.window, orient='horizontal')
        separator.pack(fill=tk.X, side=tk.BOTTOM, before=footer_frame)
        
    def update_footer(self):
        """تحديث معلومات التذييل المحسن"""
        now = datetime.datetime.now()
        
        # تنسيق الوقت والتاريخ بشكل جميل
        date_text = now.strftime("%Y/%m/%d")
        time_text = now.strftime("%H:%M:%S")
        full_text = f"📅 {date_text} ⏰ {time_text}"
        
        if hasattr(self, 'time_label'):
            self.time_label.config(text=full_text)
        
        # تحديث كل ثانية
        self.window.after(1000, self.update_footer)
        
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

if __name__ == "__main__":
    print("🧪 بدء اختبار التذييل المحسن...")
    app = FooterTestWindow()
    app.run()