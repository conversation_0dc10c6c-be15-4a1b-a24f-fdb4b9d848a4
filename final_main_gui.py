#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية النهائية لنظام إدارة أعمال الإدارة الهندسية
Final Main GUI for Engineering Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from engineering_management_system import DatabaseManager, AuthenticationManager
import datetime

class MainApplication:
    """التطبيق الرئيسي مع التذييل المحسن"""
    
    def __init__(self):
        # إعداد قاعدة البيانات والمصادقة
        self.db_manager = DatabaseManager("data/engineering_system.db")
        self.auth_manager = AuthenticationManager(self.db_manager)
        self.window = None
        self.setup_fonts()
        
    def setup_fonts(self):
        """إعداد الخطوط المحسنة"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.menu_font = ("Segoe UI", 11)
        self.footer_font = ("Segoe UI", 10, "bold")  # خط أزرق غامق وثقيل للتذييل
        
    def start(self):
        """بدء التطبيق مع تسجيل دخول مباشر للاختبار"""
        print("🚀 بدء التطبيق...")
        
        # تسجيل دخول تلقائي للاختبار
        if self.auth_manager.authenticate("admin", "admin123"):
            print("✅ تسجيل الدخول نجح")
            self.show_main_window()
        else:
            print("❌ فشل في تسجيل الدخول")
            messagebox.showerror("خطأ", "فشل في تسجيل الدخول التلقائي")
    
    def show_main_window(self):
        """عرض النافذة الرئيسية المحسنة"""
        print("🔍 إنشاء النافذة الرئيسية...")
        
        self.window = ttk_bs.Window(
            title="🏗️ نظام إدارة أعمال الإدارة الهندسية - الإصدار المحسن",
            themename="cosmo",
            size=(1200, 800)
        )
        
        # توسيط النافذة
        self.center_window(self.window, 1200, 800)
        print("✅ توسيط النافذة")
        
        # تطبيق أنماط مخصصة
        self.apply_custom_styles()
        print("✅ تطبيق الأنماط المخصصة")
        
        # إنشاء القوائم
        self.create_menu()
        print("✅ إنشاء القوائم")
        
        # إنشاء الواجهة الرئيسية
        self.create_main_interface()
        print("✅ إنشاء الواجهة الرئيسية")
        
        # إنشاء التذييل المحسن
        self.create_footer()
        print("✅ إنشاء التذييل")
        
        # بدء تحديث التذييل
        self.update_footer()
        print("✅ تحديث التذييل")
        
        print("🎉 النافذة الرئيسية جاهزة للعرض!")
        self.window.mainloop()
    
    def center_window(self, window, width, height):
        """توسيط النافذة في الشاشة"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    def apply_custom_styles(self):
        """تطبيق الأنماط المخصصة للواجهة"""
        style = ttk_bs.Style()
        
        # تخصيص ألوان الأزرار
        style.configure("Custom.TButton", 
                       foreground="#1e3a8a",
                       borderwidth=2)
        
        # تخصيص ألوان التسميات
        style.configure("Custom.TLabel",
                       foreground="#1e3a8a")
        
        # تخصيص ألوان الإطارات
        style.configure("Custom.TLabelFrame",
                       foreground="#1e3a8a",
                       borderwidth=2)
    
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="إعدادات النظام", command=self.show_settings)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.window.quit)
        
        # قائمة المشاريع
        projects_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المشاريع", menu=projects_menu)
        projects_menu.add_command(label="إدارة المشاريع", command=self.show_projects)
        projects_menu.add_command(label="مشروع جديد", command=self.new_project)
        
        # قائمة المباني
        buildings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المباني والمرافق", menu=buildings_menu)
        buildings_menu.add_command(label="إدارة المباني", command=self.show_buildings)
        buildings_menu.add_command(label="مبنى جديد", command=self.new_building)
        
        # قائمة الصيانة
        maintenance_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الصيانة", menu=maintenance_menu)
        maintenance_menu.add_command(label="بلاغات الأعطال", command=self.show_maintenance_requests)
        maintenance_menu.add_command(label="الصيانة الوقائية", command=self.show_preventive_maintenance)
        maintenance_menu.add_command(label="سجل الصيانة", command=self.show_maintenance_history)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="لوحة التحكم", command=self.show_dashboard)
        reports_menu.add_command(label="تقارير المشاريع", command=self.show_project_reports)
        reports_menu.add_command(label="تقارير الصيانة", command=self.show_maintenance_reports)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_help)
        help_menu.add_command(label="حول النظام", command=self.show_about)
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية المحسنة"""
        # شريط الحالة العلوي المحسن
        status_frame = ttk_bs.LabelFrame(
            self.window, 
            text="معلومات المستخدم",
            bootstyle="info",
            padding=10
        )
        status_frame.pack(fill=tk.X, padx=15, pady=10)
        
        user_info = f"👤 المستخدم: {self.auth_manager.current_user['full_name']} | 🔐 الصلاحية: {self.auth_manager.current_user['role']}"
        status_label = ttk_bs.Label(
            status_frame, 
            text=user_info, 
            font=self.normal_font,
            foreground="#1e3a8a",
            bootstyle="info"
        )
        status_label.pack(side=tk.LEFT)
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        
        # إنشاء دفتر الملاحظات (Notebook) للتبويبات المحسن
        self.notebook = ttk_bs.Notebook(main_frame, bootstyle="primary")
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تطبيق خط مخصص للتبويبات
        style = ttk_bs.Style()
        style.configure("TNotebook.Tab", 
                       font=self.normal_font,
                       foreground="#1e3a8a",
                       padding=[20, 10])
        
        # تبويب لوحة التحكم
        self.dashboard_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.dashboard_frame, text="🏠 لوحة التحكم")
        
        # تبويب المشاريع
        self.projects_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.projects_frame, text="🏗️ المشاريع")
        
        # تبويب المباني
        self.buildings_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.buildings_frame, text="🏢 المباني والمرافق")
        
        # تبويب الصيانة
        self.maintenance_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.maintenance_frame, text="🔧 الصيانة")
        
        # إنشاء محتوى لوحة التحكم
        self.create_dashboard()
        
        # إنشاء محتوى التبويبات الأخرى
        self.create_projects_tab()
        self.create_buildings_tab()
        self.create_maintenance_tab()
    
    def create_dashboard(self):
        """إنشاء لوحة التحكم المحسنة"""
        # عنوان لوحة التحكم المحسن مع تدرج لوني
        title_frame = ttk_bs.Frame(self.dashboard_frame)
        title_frame.pack(fill=tk.X, pady=20)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🏗️ لوحة التحكم الرئيسية 🏗️",
            font=self.title_font,
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack()
        
        subtitle_label = ttk_bs.Label(
            title_frame,
            text="مرحباً بك في نظام إدارة أعمال الإدارة الهندسية",
            font=self.normal_font,
            bootstyle="secondary"
        )
        subtitle_label.pack(pady=(5, 0))
        
        # إطار الإحصائيات المحسن
        stats_frame = ttk_bs.LabelFrame(
            self.dashboard_frame, 
            text="📊 الإحصائيات السريعة", 
            padding=15,
            bootstyle="info"
        )
        stats_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # إحصائيات في صفوف مع تصميم محسن
        stats_row1 = ttk_bs.Frame(stats_frame)
        stats_row1.pack(fill=tk.X, pady=10)
        
        # بطاقات الإحصائيات
        self.create_stat_card(stats_row1, "🏗️ المشاريع النشطة", "12", "primary")
        self.create_stat_card(stats_row1, "🏢 المباني المسجلة", "8", "success")
        self.create_stat_card(stats_row1, "🔧 طلبات الصيانة", "5", "warning")
        self.create_stat_card(stats_row1, "✅ المهام المكتملة", "25", "info")
        
        # إطار معلومات التذييل
        footer_info_frame = ttk_bs.LabelFrame(
            self.dashboard_frame,
            text="🎯 مواصفات التذييل المحسن",
            padding=15,
            bootstyle="success"
        )
        footer_info_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        
        footer_info_text = """✅ اسم المستخدم في اليسار مع أيقونة 👤
✅ اسم البرنامج مع حقوق النشر © 2025 في الوسط مع أيقونة 🏗️
✅ التاريخ والوقت في اليمين مع أيقونات 📅⏰
✅ خط أزرق غامق وثقيل قليلاً (#1e3a8a)
✅ تحديث تلقائي للوقت كل ثانية
✅ خط فاصل علوي للتذييل"""
        
        footer_info_label = ttk_bs.Label(
            footer_info_frame,
            text=footer_info_text,
            font=self.normal_font,
            foreground="#1e3a8a",
            justify=tk.LEFT
        )
        footer_info_label.pack(anchor=tk.W, pady=10)
    
    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        # عنوان التبويب
        title_frame = ttk_bs.Frame(self.projects_frame)
        title_frame.pack(fill=tk.X, pady=20)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🏗️ إدارة المشاريع الهندسية 🏗️",
            font=self.title_font,
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack()
        
        # معلومات المشاريع
        info_frame = ttk_bs.LabelFrame(
            self.projects_frame,
            text="📈 معلومات المشاريع",
            padding=20,
            bootstyle="secondary"
        )
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        info_text = """🏗️ نظام إدارة المشاريع الهندسية يوفر:

✅ إدارة شاملة لجميع المشاريع
✅ تتبع حالة المشاريع ونسب الإنجاز
✅ إدارة المقاولين والتكاليف
✅ تقارير مفصلة عن أداء المشاريع
✅ واجهة سهلة الاستخدام ومحسنة"""
        
        info_label = ttk_bs.Label(
            info_frame,
            text=info_text,
            font=self.normal_font,
            foreground="#1e3a8a",
            justify=tk.LEFT
        )
        info_label.pack(anchor=tk.W)
    
    def create_buildings_tab(self):
        """إنشاء تبويب المباني"""
        # عنوان التبويب
        title_frame = ttk_bs.Frame(self.buildings_frame)
        title_frame.pack(fill=tk.X, pady=20)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🏢 إدارة المباني والمرافق 🏢",
            font=self.title_font,
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack()
        
        # رسالة قيد التطوير
        dev_frame = ttk_bs.LabelFrame(
            self.buildings_frame,
            text="🚧 قيد التطوير",
            padding=30,
            bootstyle="warning"
        )
        dev_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        dev_text = """🏢 وحدة إدارة المباني والمرافق قيد التطوير

ستتضمن الميزات التالية:
🏗️ تسجيل وإدارة المباني
📋 متابعة حالة المرافق
🔧 جدولة أعمال الصيانة
📊 تقارير حالة المباني"""
        
        dev_label = ttk_bs.Label(
            dev_frame,
            text=dev_text,
            font=self.header_font,
            foreground="#1e3a8a",
            justify=tk.CENTER
        )
        dev_label.pack(expand=True)
    
    def create_maintenance_tab(self):
        """إنشاء تبويب الصيانة"""
        # عنوان التبويب
        title_frame = ttk_bs.Frame(self.maintenance_frame)
        title_frame.pack(fill=tk.X, pady=20)
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🔧 إدارة أعمال الصيانة 🔧",
            font=self.title_font,
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack()
        
        # رسالة قيد التطوير
        dev_frame = ttk_bs.LabelFrame(
            self.maintenance_frame,
            text="🚧 قيد التطوير",
            padding=30,
            bootstyle="danger"
        )
        dev_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        dev_text = """🔧 وحدة إدارة أعمال الصيانة قيد التطوير

ستتضمن الميزات التالية:
📝 تسجيل بلاغات الأعطال
🔄 الصيانة الوقائية المجدولة
📋 سجل أعمال الصيانة
👷 إدارة فرق الصيانة
📊 تقارير الصيانة والأعطال"""
        
        dev_label = ttk_bs.Label(
            dev_frame,
            text=dev_text,
            font=self.header_font,
            foreground="#1e3a8a",
            justify=tk.CENTER
        )
        dev_label.pack(expand=True)
    
    def create_stat_card(self, parent, title, value, style):
        """إنشاء بطاقة إحصائية محسنة"""
        # إطار البطاقة مع تصميم محسن
        card_frame = ttk_bs.LabelFrame(
            parent, 
            text="", 
            padding=15,
            bootstyle=style
        )
        card_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=8, pady=5)
        
        # عنوان البطاقة
        title_label = ttk_bs.Label(
            card_frame, 
            text=title, 
            font=self.normal_font,
            bootstyle=style,
            foreground="#1e3a8a"
        )
        title_label.pack(pady=(0, 8))
        
        # قيمة الإحصائية
        value_label = ttk_bs.Label(
            card_frame, 
            text=value, 
            font=("Segoe UI", 24, "bold"), 
            bootstyle=style,
            foreground="#1e3a8a"
        )
        value_label.pack()
    
    def create_footer(self):
        """إنشاء تذييل الشاشة الرئيسية المحسن"""
        print("🔍 إنشاء التذييل المحسن...")
        
        # إطار التذييل
        footer_frame = ttk_bs.Frame(self.window)
        footer_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # إطار فرعي للمحتوى
        content_frame = ttk_bs.Frame(footer_frame)
        content_frame.pack(fill=tk.X, padx=15, pady=12)
        
        # اسم المستخدم (يسار) مع أيقونة
        user_name = self.auth_manager.current_user.get('full_name', 'غير محدد')
        self.user_label = ttk_bs.Label(
            content_frame,
            text=f"👤 المستخدم: {user_name}",
            font=("Segoe UI", 10, "bold"),  # خط ثقيل قليلاً
            foreground="#1e3a8a",  # أزرق غامق
            bootstyle="primary"
        )
        self.user_label.pack(side=tk.LEFT)
        
        # اسم البرنامج مع حقوق النشر (وسط) مع تصميم مميز
        program_label = ttk_bs.Label(
            content_frame,
            text="🏗️ نظام إدارة أعمال الإدارة الهندسية © 2025 🏗️",
            font=("Segoe UI", 10, "bold"),  # خط ثقيل قليلاً
            foreground="#1e3a8a",  # أزرق غامق
            bootstyle="primary"
        )
        program_label.pack(expand=True)
        
        # الوقت والتاريخ (يمين) مع أيقونة
        self.time_label = ttk_bs.Label(
            content_frame,
            text="",
            font=("Segoe UI", 10, "bold"),  # خط ثقيل قليلاً
            foreground="#1e3a8a",  # أزرق غامق
            bootstyle="primary"
        )
        self.time_label.pack(side=tk.RIGHT)
        
        # إضافة خط فاصل علوي للتذييل
        separator = ttk_bs.Separator(self.window, orient='horizontal')
        separator.pack(fill=tk.X, side=tk.BOTTOM, before=footer_frame)
        
        print("✅ التذييل تم إنشاؤه بنجاح!")
    
    def update_footer(self):
        """تحديث معلومات التذييل المحسن"""
        now = datetime.datetime.now()
        
        # تنسيق الوقت والتاريخ بشكل جميل
        date_text = now.strftime("%Y/%m/%d")
        time_text = now.strftime("%H:%M:%S")
        full_text = f"📅 {date_text} ⏰ {time_text}"
        
        if hasattr(self, 'time_label') and self.time_label.winfo_exists():
            self.time_label.config(text=full_text)
        
        # تحديث كل ثانية
        if self.window and self.window.winfo_exists():
            self.window.after(1000, self.update_footer)
    
    # وظائف القوائم
    def show_settings(self): 
        messagebox.showinfo("إعدادات", "وحدة الإعدادات قيد التطوير")
        
    def logout(self): 
        self.auth_manager.logout()
        self.window.destroy()
        self.start()
    
    def show_projects(self):
        messagebox.showinfo("المشاريع", "وحدة إدارة المشاريع قيد التطوير")

    def new_project(self):
        messagebox.showinfo("مشروع جديد", "وحدة إضافة المشاريع قيد التطوير")
            
    def show_buildings(self):
        messagebox.showinfo("المباني", "وحدة إدارة المباني قيد التطوير")

    def new_building(self):
        messagebox.showinfo("مبنى جديد", "وحدة إضافة المباني قيد التطوير")

    def show_maintenance_requests(self):
        messagebox.showinfo("الصيانة", "وحدة إدارة الصيانة قيد التطوير")

    def show_preventive_maintenance(self):
        self.show_maintenance_requests()

    def show_maintenance_history(self):
        self.show_maintenance_requests()

    def show_dashboard(self):
        messagebox.showinfo("التقارير", "وحدة التقارير قيد التطوير")

    def show_project_reports(self):
        self.show_dashboard()

    def show_maintenance_reports(self):
        self.show_dashboard()
        
    def show_help(self): 
        messagebox.showinfo("مساعدة", "دليل المستخدم قيد التطوير")
        
    def show_about(self):
        about_text = """🏗️ نظام إدارة أعمال الإدارة الهندسية - الإصدار المحسن 🏗️

📋 الإصدار: 2.0 Enhanced
🎨 التحسينات الجديدة:
   ✅ جميع الشاشات تفتح في وسط الشاشة
   ✅ خطوط أكبر وأوضح
   ✅ ألوان وأشكال مميزة
   ✅ تذييل محسن مع الوقت والتاريخ
   ✅ خط أزرق غامق وثقيل
   ✅ واجهة مستخدم محسنة بالكامل

👨‍💻 تطوير: فريق التطوير المتخصص
📅 تاريخ التحديث: 2024/01/15
🎯 الهدف: تحسين تجربة المستخدم"""
        messagebox.showinfo("🏗️ حول النظام المحسن", about_text)

if __name__ == "__main__":
    print("🚀 بدء تشغيل النظام المحسن مع التذييل...")
    print("📋 بيانات تسجيل الدخول التلقائي:")
    print("   👤 اسم المستخدم: admin")
    print("   🔒 كلمة المرور: admin123")
    print()
    
    app = MainApplication()
    app.start()