#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع الميزات الجديدة
Test All New Features
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from engineering_management_system import DatabaseManager, AuthenticationManager

def test_project_dialog():
    """اختبار نافذة إضافة مشروع جديد"""
    try:
        # إنشاء نافذة مؤقتة
        temp_root = tk.Tk()
        temp_root.withdraw()
        
        # إنشاء قاعدة البيانات ومدير المصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # محاكاة تسجيل دخول
        auth_manager.current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin'
        }
        
        from simple_project_management import ProjectDialog
        dialog = ProjectDialog(temp_root, db_manager, auth_manager, "إضافة مشروع جديد")
        result = dialog.show()
        
        if result:
            messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح!")
        
        temp_root.destroy()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في اختبار نافذة المشروع: {str(e)}")

def test_building_dialog():
    """اختبار نافذة إضافة مبنى جديد"""
    try:
        # إنشاء نافذة مؤقتة
        temp_root = tk.Tk()
        temp_root.withdraw()
        
        # إنشاء قاعدة البيانات ومدير المصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # محاكاة تسجيل دخول
        auth_manager.current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin'
        }
        
        from buildings_management import BuildingDialog
        dialog = BuildingDialog(temp_root, db_manager, auth_manager, "إضافة مبنى جديد")
        result = dialog.show()
        
        if result:
            messagebox.showinfo("نجح", "تم حفظ المبنى بنجاح!")
        
        temp_root.destroy()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في اختبار نافذة المبنى: {str(e)}")

def test_maintenance_dialog():
    """اختبار نافذة إضافة بلاغ صيانة جديد"""
    try:
        # إنشاء نافذة مؤقتة
        temp_root = tk.Tk()
        temp_root.withdraw()
        
        # إنشاء قاعدة البيانات ومدير المصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # محاكاة تسجيل دخول
        auth_manager.current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin'
        }
        
        from maintenance_management import MaintenanceRequestDialog
        dialog = MaintenanceRequestDialog(temp_root, db_manager, auth_manager, "إضافة بلاغ صيانة جديد")
        result = dialog.show()
        
        if result:
            messagebox.showinfo("نجح", "تم حفظ بلاغ الصيانة بنجاح!")
        
        temp_root.destroy()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في اختبار نافذة الصيانة: {str(e)}")

def test_reports_window():
    """اختبار شاشة التقارير مع الطباعة"""
    try:
        # إنشاء نافذة مؤقتة
        temp_root = tk.Tk()
        temp_root.withdraw()
        
        # إنشاء قاعدة البيانات ومدير المصادقة
        db_manager = DatabaseManager("data/engineering_system.db")
        auth_manager = AuthenticationManager(db_manager)
        
        # محاكاة تسجيل دخول
        auth_manager.current_user = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin'
        }
        
        from reports_management import ReportsManagementWindow
        reports_window = ReportsManagementWindow(temp_root, db_manager, auth_manager)
        reports_window.show()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في اختبار شاشة التقارير: {str(e)}")

def main():
    """الدالة الرئيسية"""
    
    # إنشاء نافذة اختبار
    root = ttk_bs.Window(
        title="🧪 اختبار جميع الميزات الجديدة",
        themename="cosmo",
        size=(600, 500)
    )
    
    # توسيط النافذة
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    # إطار رئيسي
    main_frame = ttk_bs.Frame(root, padding=30)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # عنوان
    title_label = ttk_bs.Label(
        main_frame,
        text="🧪 اختبار جميع الميزات الجديدة 🧪",
        bootstyle="primary",
        foreground="#1e3a8a"
    )
    title_label.pack(pady=(0, 30))
    
    # معلومات
    info_label = ttk_bs.Label(
        main_frame,
        text="اختبار النوافذ والميزات الجديدة المحسنة",
        bootstyle="secondary"
    )
    info_label.pack(pady=(0, 30))
    
    # إطار الأزرار
    buttons_frame = ttk_bs.Frame(main_frame)
    buttons_frame.pack(fill=tk.X, pady=20)
    
    # الصف الأول
    row1_frame = ttk_bs.Frame(buttons_frame)
    row1_frame.pack(fill=tk.X, pady=10)
    
    ttk_bs.Button(
        row1_frame,
        text="📝 اختبار نافذة المشروع الجديد",
        command=test_project_dialog,
        bootstyle="primary",
        width=35
    ).pack(pady=10, ipady=10)
    
    ttk_bs.Button(
        row1_frame,
        text="🏢 اختبار نافذة المبنى الجديد",
        command=test_building_dialog,
        bootstyle="info",
        width=35
    ).pack(pady=10, ipady=10)
    
    ttk_bs.Button(
        row1_frame,
        text="🔧 اختبار نافذة بلاغ الصيانة",
        command=test_maintenance_dialog,
        bootstyle="warning",
        width=35
    ).pack(pady=10, ipady=10)
    
    ttk_bs.Button(
        row1_frame,
        text="📊 اختبار شاشة التقارير والطباعة",
        command=test_reports_window,
        bootstyle="success",
        width=35
    ).pack(pady=10, ipady=10)
    
    # معلومات إضافية
    info2_label = ttk_bs.Label(
        main_frame,
        text="جميع النوافذ محسنة مع:\n• أحجام مناسبة\n• توسيط في الشاشة\n• أزرار حفظ وإلغاء\n• بيانات كاملة\n• طباعة التقارير",
        bootstyle="secondary",
        justify=tk.CENTER
    )
    info2_label.pack(pady=30)
    
    # زر الخروج
    ttk_bs.Button(
        main_frame,
        text="❌ خروج",
        command=root.quit,
        bootstyle="danger",
        width=15
    ).pack(pady=20, ipady=10)
    
    root.mainloop()

if __name__ == "__main__":
    main()