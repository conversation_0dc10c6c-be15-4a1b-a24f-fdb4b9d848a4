-- SQL script to migrate buildings table to new schema with additional columns

BEGIN TRANSACTION;

-- Rename old table
ALTER TABLE buildings RENAME TO buildings_old;

-- Create new table with updated schema
CREATE TABLE buildings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    type TEXT,
    location TEXT,
    floors INTEGER,
    area TEXT,
    status TEXT,
    construction_year TEXT,
    owner TEXT,
    structural_condition TEXT,
    contractor TEXT,
    cost REAL,
    notes TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Copy data from old table to new table
INSERT INTO buildings (id, name, owner, structural_condition)
SELECT id, name, owner, structural_condition FROM buildings_old;

-- Drop old table
DROP TABLE buildings_old;

COMMIT;

-- Note: This migration preserves existing data and adds new columns with NULL/default values.
-- Please backup your database before running this script.
