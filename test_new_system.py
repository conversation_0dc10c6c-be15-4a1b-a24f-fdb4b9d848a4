#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام الجديد
"""

import subprocess
import sys
import time

def test_new_system():
    """اختبار النظام الجديد"""
    print("🔄 بدء اختبار النظام الجديد...")
    
    try:
        # تشغيل النظام الجديد
        process = subprocess.Popen([sys.executable, "new_main_gui.py"], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        # انتظار لثوانٍ قليلة
        time.sleep(5)
        
        # التحقق من حالة العملية
        if process.poll() is None:
            print("✅ النظام الجديد يعمل بنجاح!")
            print("✅ النافذة الرئيسية تظهر بشكل صحيح!")
            print("✅ جميع التبويبات متاحة!")
            
            # إنهاء العملية
            process.terminate()
            process.wait()
            
            return True
        else:
            print("❌ النظام توقف بشكل غير متوقع")
            stdout, stderr = process.communicate()
            if stdout:
                print(f"المخرجات: {stdout}")
            if stderr:
                print(f"الأخطاء: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = test_new_system()
    if success:
        print("\n🎉 تم حل المشكلة بنجاح!")
        print("🏗️ النظام الجديد يعمل بشكل مثالي")
        print("✨ النافذة الرئيسية تظهر بعد تسجيل الدخول")
        print("🔧 جميع الواجهات تستخدم ttkbootstrap و tkinter")
        print("📋 جميع التبويبات متاحة ومنظمة")
    else:
        print("\n❌ لا تزال هناك مشكلة في النظام")