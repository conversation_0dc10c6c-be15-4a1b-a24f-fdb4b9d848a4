#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التطبيق الرئيسي مع التذييل المحسن
Test Main Application with Enhanced Footer
"""

import sys
import os

# إضافة المسار الحالي لـ Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from main_gui import MainApplication
    print("✅ تم تحميل التطبيق الرئيسي بنجاح")
    
    print("🚀 بدء تشغيل التطبيق مع التذييل المحسن...")
    print("📋 بيانات تسجيل الدخول:")
    print("   👤 اسم المستخدم: admin")
    print("   🔒 كلمة المرور: admin123")
    print()
    print("🎯 التذييل المحسن يحتوي على:")
    print("   📍 اسم المستخدم في اليسار")
    print("   📍 اسم البرنامج مع حقوق النشر © 2025 في الوسط")
    print("   📍 التاريخ والوقت في اليمين")
    print("   📍 خط أزرق غامق وثقيل")
    print()
    
    # تشغيل التطبيق
    app = MainApplication()
    app.start()
    
except ImportError as e:
    print(f"❌ خطأ في استيراد التطبيق: {e}")
    print("🔧 تأكد من وجود جميع الملفات المطلوبة")
except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    print("🔧 تحقق من إعدادات النظام")