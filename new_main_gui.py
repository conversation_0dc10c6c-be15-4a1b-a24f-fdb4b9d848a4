#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة أعمال الإدارة الهندسية - واجهة جديدة مبسطة
Engineering Management System - New Simplified Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from engineering_management_system import DatabaseManager, AuthenticationManager
import datetime
import logging

class EngineeringManagementApp:
    """التطبيق الرئيسي لنظام إدارة أعمال الإدارة الهندسية"""
    
    def __init__(self):
        self.db_manager = DatabaseManager("data/engineering_system.db")
        self.auth_manager = AuthenticationManager(self.db_manager)
        self.root = None
        self.current_window = None
        
    def start(self):
        """بدء التطبيق"""
        print("🚀 بدء تشغيل نظام إدارة أعمال الإدارة الهندسية...")
        self.show_login_window()
    
    def show_login_window(self):
        """عرض نافذة تسجيل الدخول"""
        print("🔐 عرض نافذة تسجيل الدخول...")
        
        # إنشاء النافذة الرئيسية
        self.root = ttk_bs.Window(
            title="🏗️ نظام إدارة أعمال الإدارة الهندسية - تسجيل الدخول",
            themename="cosmo",
            size=(500, 400),
            resizable=(False, False)
        )
        
        # توسيط النافذة
        self.center_window(self.root, 500, 400)
        
        # إنشاء الواجهة
        self.create_login_interface()
        
        # عرض النافذة
        self.root.mainloop()
    
    def create_login_interface(self):
        """إنشاء واجهة تسجيل الدخول"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.root, padding=30)
        main_frame.pack(fill=BOTH, expand=True)
        
        # شعار وعنوان
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(pady=(0, 30))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🏗️ نظام إدارة أعمال الإدارة الهندسية",
            font=("Segoe UI", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        subtitle_label = ttk_bs.Label(
            title_frame,
            text="Engineering Management System",
            font=("Segoe UI", 12),
            bootstyle="secondary"
        )
        subtitle_label.pack(pady=(5, 0))
        
        # إطار تسجيل الدخول
        login_frame = ttk_bs.LabelFrame(
            main_frame,
            text="🔐 تسجيل الدخول",
            padding=20,
            bootstyle="info"
        )
        login_frame.pack(fill=X, pady=20)
        
        # حقل اسم المستخدم
        ttk_bs.Label(
            login_frame,
            text="👤 اسم المستخدم:",
            font=("Segoe UI", 11)
        ).pack(anchor=W, pady=(0, 5))
        
        self.username_var = tk.StringVar(value="admin")
        self.username_entry = ttk_bs.Entry(
            login_frame,
            textvariable=self.username_var,
            font=("Segoe UI", 11),
            width=30
        )
        self.username_entry.pack(fill=X, pady=(0, 15))
        
        # حقل كلمة المرور
        ttk_bs.Label(
            login_frame,
            text="🔒 كلمة المرور:",
            font=("Segoe UI", 11)
        ).pack(anchor=W, pady=(0, 5))
        
        self.password_var = tk.StringVar(value="admin123")
        self.password_entry = ttk_bs.Entry(
            login_frame,
            textvariable=self.password_var,
            font=("Segoe UI", 11),
            show="*",
            width=30
        )
        self.password_entry.pack(fill=X, pady=(0, 20))
        
        # أزرار
        buttons_frame = ttk_bs.Frame(login_frame)
        buttons_frame.pack(fill=X)
        
        login_btn = ttk_bs.Button(
            buttons_frame,
            text="🚀 تسجيل الدخول",
            command=self.login,
            bootstyle="success",
            width=20
        )
        login_btn.pack(side=LEFT, padx=(0, 10))
        
        exit_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ خروج",
            command=self.root.quit,
            bootstyle="danger",
            width=15
        )
        exit_btn.pack(side=RIGHT)
        
        # ربط مفتاح Enter
        self.root.bind('<Return>', lambda e: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        print(f"🔄 محاولة تسجيل الدخول للمستخدم: {username}")
        
        if self.auth_manager.authenticate(username, password):
            print("✅ تم تسجيل الدخول بنجاح!")
            messagebox.showinfo("نجح", f"مرحباً {self.auth_manager.current_user['full_name']}")
            
            # إغلاق نافذة تسجيل الدخول
            self.root.destroy()
            
            # عرض النافذة الرئيسية
            self.show_main_window()
        else:
            print("❌ فشل تسجيل الدخول")
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        print("🏠 عرض النافذة الرئيسية...")
        
        try:
            # إنشاء النافذة الرئيسية
            self.root = ttk_bs.Window(
                title="🏗️ نظام إدارة أعمال الإدارة الهندسية - النافذة الرئيسية",
                themename="cosmo",
                size=(1200, 800)
            )
            
            # توسيط النافذة
            self.center_window(self.root, 1200, 800)
            
            # إنشاء الواجهة الرئيسية
            self.create_main_interface()
            
            print("✅ النافذة الرئيسية جاهزة!")
            
            # عرض النافذة
            self.root.mainloop()
            
        except Exception as e:
            print(f"❌ خطأ في النافذة الرئيسية: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في عرض النافذة الرئيسية:\n{e}")
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # شريط المعلومات العلوي
        self.create_top_bar()
        
        # المحتوى الرئيسي
        self.create_main_content()
        
        # شريط الحالة السفلي
        self.create_status_bar()
    
    def create_top_bar(self):
        """إنشاء شريط المعلومات العلوي"""
        top_frame = ttk_bs.Frame(self.root, bootstyle="primary")
        top_frame.pack(fill=X, padx=10, pady=10)
        
        # معلومات المستخدم
        user_info = f"👤 المستخدم: {self.auth_manager.current_user['full_name']} | 🔐 الصلاحية: {self.auth_manager.current_user['role']}"
        user_label = ttk_bs.Label(
            top_frame,
            text=user_info,
            font=("Segoe UI", 12, "bold"),
            bootstyle="inverse-primary"
        )
        user_label.pack(side=LEFT, padx=10, pady=10)
        
        # أزرار سريعة
        buttons_frame = ttk_bs.Frame(top_frame)
        buttons_frame.pack(side=RIGHT, padx=10, pady=5)
        
        ttk_bs.Button(
            buttons_frame,
            text="🚪 تسجيل الخروج",
            command=self.logout,
            bootstyle="warning",
            width=15
        ).pack(side=RIGHT, padx=5)
        
        ttk_bs.Button(
            buttons_frame,
            text="⚙️ الإعدادات",
            command=self.show_settings,
            bootstyle="info",
            width=15
        ).pack(side=RIGHT, padx=5)
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار المحتوى
        content_frame = ttk_bs.Frame(self.root)
        content_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # إنشاء دفتر الملاحظات للتبويبات
        self.notebook = ttk_bs.Notebook(content_frame, bootstyle="primary")
        self.notebook.pack(fill=BOTH, expand=True)
        
        # تبويب لوحة التحكم
        self.create_dashboard_tab()
        
        # تبويب المشاريع
        self.create_projects_tab()
        
        # تبويب المباني
        self.create_buildings_tab()
        
        # تبويب الصيانة
        self.create_maintenance_tab()
        
        # تبويب التقارير
        self.create_reports_tab()
    
    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        dashboard_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="🏠 لوحة التحكم")
        
        # عنوان
        title_label = ttk_bs.Label(
            dashboard_frame,
            text="🏗️ لوحة التحكم الرئيسية",
            font=("Segoe UI", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=20)
        
        # إطار الإحصائيات
        stats_frame = ttk_bs.LabelFrame(
            dashboard_frame,
            text="📊 الإحصائيات السريعة",
            padding=20,
            bootstyle="info"
        )
        stats_frame.pack(fill=X, padx=20, pady=10)
        
        # صف الإحصائيات
        stats_row = ttk_bs.Frame(stats_frame)
        stats_row.pack(fill=X)
        
        # بطاقات الإحصائيات
        self.create_stat_card(stats_row, "🏗️ المشاريع النشطة", "5", "success")
        self.create_stat_card(stats_row, "⚠️ البلاغات المفتوحة", "3", "warning")
        self.create_stat_card(stats_row, "🏢 المباني المسجلة", "12", "info")
        self.create_stat_card(stats_row, "🔧 أعمال الصيانة", "8", "secondary")
        
        # إطار الأنشطة الحديثة
        activities_frame = ttk_bs.LabelFrame(
            dashboard_frame,
            text="📋 الأنشطة الحديثة",
            padding=20,
            bootstyle="secondary"
        )
        activities_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)
        
        # قائمة الأنشطة
        activities_text = ttk_bs.Text(
            activities_frame,
            height=10,
            font=("Segoe UI", 10)
        )
        activities_text.pack(fill=BOTH, expand=True)
        
        # إضافة بعض الأنشطة التوضيحية
        sample_activities = """
📅 2024/01/15 - 10:30 | ✅ تم إنشاء مشروع جديد: مبنى الإدارة الجديد
📅 2024/01/15 - 09:45 | 🔧 تم تسجيل بلاغ صيانة: تسريب في المبنى الرئيسي
📅 2024/01/15 - 09:15 | 📊 تم إنتاج تقرير شهري للمشاريع
📅 2024/01/14 - 16:20 | ✅ تم إكمال صيانة نظام التكييف
📅 2024/01/14 - 14:30 | 🏗️ تحديث حالة مشروع: مجمع الورش الهندسية
        """
        activities_text.insert("1.0", sample_activities.strip())
        activities_text.config(state="disabled")
    
    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        projects_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(projects_frame, text="🏗️ المشاريع")
        
        # عنوان
        title_label = ttk_bs.Label(
            projects_frame,
            text="🏗️ إدارة المشاريع الهندسية",
            font=("Segoe UI", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=20)
        
        # أزرار سريعة
        buttons_frame = ttk_bs.LabelFrame(
            projects_frame,
            text="🛠️ أدوات المشاريع",
            padding=20,
            bootstyle="info"
        )
        buttons_frame.pack(fill=X, padx=20, pady=10)
        
        btn_row = ttk_bs.Frame(buttons_frame)
        btn_row.pack()
        
        ttk_bs.Button(
            btn_row,
            text="📋 عرض جميع المشاريع",
            command=self.show_all_projects,
            bootstyle="primary",
            width=25
        ).pack(side=LEFT, padx=10)
        
        ttk_bs.Button(
            btn_row,
            text="➕ إضافة مشروع جديد",
            command=self.add_new_project,
            bootstyle="success",
            width=25
        ).pack(side=LEFT, padx=10)
        
        ttk_bs.Button(
            btn_row,
            text="📊 تقارير المشاريع",
            command=self.show_project_reports,
            bootstyle="warning",
            width=25
        ).pack(side=LEFT, padx=10)
        
        # معلومات المشاريع
        info_frame = ttk_bs.LabelFrame(
            projects_frame,
            text="📈 معلومات المشاريع",
            padding=20,
            bootstyle="secondary"
        )
        info_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)
        
        info_text = """
🏗️ نظام إدارة المشاريع الهندسية يوفر:

✅ إدارة شاملة لجميع المشاريع
✅ تتبع حالة المشاريع ونسب الإنجاز
✅ إدارة المقاولين والتكاليف
✅ تقارير مفصلة عن أداء المشاريع
✅ واجهة سهلة الاستخدام ومحسنة
✅ نظام إشعارات للمواعيد المهمة
        """
        
        info_label = ttk_bs.Label(
            info_frame,
            text=info_text.strip(),
            font=("Segoe UI", 11),
            justify=LEFT
        )
        info_label.pack(anchor=W)
    
    def create_buildings_tab(self):
        """إنشاء تبويب المباني"""
        buildings_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(buildings_frame, text="🏢 المباني")
        
        # عنوان
        title_label = ttk_bs.Label(
            buildings_frame,
            text="🏢 إدارة المباني والمرافق",
            font=("Segoe UI", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=20)
        
        # رسالة قيد التطوير
        dev_frame = ttk_bs.LabelFrame(
            buildings_frame,
            text="🚧 قيد التطوير",
            padding=30,
            bootstyle="warning"
        )
        dev_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        dev_text = """
🏢 وحدة إدارة المباني والمرافق قيد التطوير

ستتضمن الميزات التالية:
🏗️ تسجيل وإدارة المباني
📋 متابعة حالة المرافق
🔧 جدولة أعمال الصيانة
📊 تقارير حالة المباني
🗺️ خرائط المباني والمرافق
        """
        
        dev_label = ttk_bs.Label(
            dev_frame,
            text=dev_text.strip(),
            font=("Segoe UI", 12),
            justify=CENTER
        )
        dev_label.pack(expand=True, pady=(0, 20))
        
        # زر اختبار
        test_btn = ttk_bs.Button(
            dev_frame,
            text="🏢 اختبار نافذة إضافة مبنى",
            command=self.test_building_dialog,
            bootstyle="info"
        )
        test_btn.pack()
    
    def create_maintenance_tab(self):
        """إنشاء تبويب الصيانة"""
        maintenance_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(maintenance_frame, text="🔧 الصيانة")
        
        # عنوان
        title_label = ttk_bs.Label(
            maintenance_frame,
            text="🔧 إدارة أعمال الصيانة",
            font=("Segoe UI", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=20)
        
        # رسالة قيد التطوير
        dev_frame = ttk_bs.LabelFrame(
            maintenance_frame,
            text="🚧 قيد التطوير",
            padding=30,
            bootstyle="danger"
        )
        dev_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        dev_text = """
🔧 وحدة إدارة أعمال الصيانة قيد التطوير

ستتضمن الميزات التالية:
📝 تسجيل بلاغات الأعطال
🔄 الصيانة الوقائية المجدولة
📋 سجل أعمال الصيانة
👷 إدارة فرق الصيانة
📊 تقارير الصيانة والأعطال
⏰ نظام تذكير بمواعيد الصيانة
        """
        
        dev_label = ttk_bs.Label(
            dev_frame,
            text=dev_text.strip(),
            font=("Segoe UI", 12),
            justify=CENTER
        )
        dev_label.pack(expand=True, pady=(0, 20))
        
        # زر اختبار
        test_btn = ttk_bs.Button(
            dev_frame,
            text="🔧 اختبار نافذة بلاغ صيانة",
            command=self.test_maintenance_dialog,
            bootstyle="warning"
        )
        test_btn.pack()
    
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        reports_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(reports_frame, text="📊 التقارير")
        
        # عنوان
        title_label = ttk_bs.Label(
            reports_frame,
            text="📊 التقارير والإحصائيات",
            font=("Segoe UI", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=20)
        
        # أنواع التقارير
        reports_frame_content = ttk_bs.LabelFrame(
            reports_frame,
            text="📈 أنواع التقارير المتاحة",
            padding=20,
            bootstyle="info"
        )
        reports_frame_content.pack(fill=BOTH, expand=True, padx=20, pady=10)
        
        reports_text = """
📊 التقارير المتاحة في النظام:

📋 تقارير المشاريع:
   • تقرير حالة المشاريع
   • تقرير التكاليف والميزانيات
   • تقرير نسب الإنجاز

🏢 تقارير المباني:
   • تقرير حالة المباني
   • تقرير استخدام المرافق
   • تقرير الصيانة المطلوبة

🔧 تقارير الصيانة:
   • تقرير البلاغات
   • تقرير الصيانة الوقائية
   • تقرير التكاليف

📈 تقارير إدارية:
   • تقرير الأداء العام
   • تقرير الإحصائيات الشهرية
   • تقرير المؤشرات الرئيسية
        """
        
        reports_label = ttk_bs.Label(
            reports_frame_content,
            text=reports_text.strip(),
            font=("Segoe UI", 11),
            justify=LEFT
        )
        reports_label.pack(anchor=W)
    
    def create_stat_card(self, parent, title, value, style):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk_bs.LabelFrame(
            parent,
            text="",
            padding=15,
            bootstyle=style
        )
        card_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=5)
        
        # عنوان البطاقة
        title_label = ttk_bs.Label(
            card_frame,
            text=title,
            font=("Segoe UI", 10, "bold"),
            bootstyle=style
        )
        title_label.pack()
        
        # قيمة الإحصائية
        value_label = ttk_bs.Label(
            card_frame,
            text=value,
            font=("Segoe UI", 24, "bold"),
            bootstyle=style
        )
        value_label.pack(pady=10)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة السفلي"""
        status_frame = ttk_bs.Frame(self.root, bootstyle="secondary")
        status_frame.pack(fill=X, side=BOTTOM)
        
        # معلومات المستخدم
        user_name = self.auth_manager.current_user.get('full_name', 'غير محدد')
        self.user_status_label = ttk_bs.Label(
            status_frame,
            text=f"👤 {user_name}",
            font=("Segoe UI", 9),
            bootstyle="inverse-secondary"
        )
        self.user_status_label.pack(side=LEFT, padx=10, pady=5)
        
        # اسم النظام
        system_label = ttk_bs.Label(
            status_frame,
            text="🏗️ نظام إدارة أعمال الإدارة الهندسية © 2025",
            font=("Segoe UI", 9),
            bootstyle="inverse-secondary"
        )
        system_label.pack(expand=True, pady=5)
        
        # الوقت والتاريخ
        self.time_label = ttk_bs.Label(
            status_frame,
            text="",
            font=("Segoe UI", 9),
            bootstyle="inverse-secondary"
        )
        self.time_label.pack(side=RIGHT, padx=10, pady=5)
        
        # تحديث الوقت
        self.update_time()
    
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        now = datetime.datetime.now()
        time_text = now.strftime("📅 %Y/%m/%d ⏰ %H:%M:%S")
        self.time_label.config(text=time_text)
        
        # تحديث كل ثانية
        self.root.after(1000, self.update_time)
    
    def center_window(self, window, width, height):
        """توسيط النافذة في الشاشة"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    # دوال الأحداث
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تأكيد", "هل تريد تسجيل الخروج؟")
        if result:
            self.auth_manager.logout()
            self.root.destroy()
            self.start()
    
    def show_settings(self):
        """عرض الإعدادات"""
        messagebox.showinfo("الإعدادات", "نافذة الإعدادات قيد التطوير")
    
    def show_all_projects(self):
        """عرض جميع المشاريع"""
        from enhanced_dialogs import ProjectDialog
        messagebox.showinfo("المشاريع", "نافذة عرض المشاريع قيد التطوير")
    
    def add_new_project(self):
        """إضافة مشروع جديد"""
        try:
            from enhanced_dialogs import ProjectDialog
            dialog = ProjectDialog(self.root, "إضافة مشروع جديد")
            result = dialog.show()
            if result:
                print("تم إضافة مشروع جديد:", result)
        except ImportError:
            messagebox.showinfo("مشروع جديد", "نافذة إضافة مشروع جديد قيد التطوير")
    
    def show_project_reports(self):
        """عرض تقارير المشاريع"""
        messagebox.showinfo("تقارير المشاريع", "نافذة تقارير المشاريع قيد التطوير")
    
    def test_building_dialog(self):
        """اختبار نافذة إضافة مبنى"""
        try:
            from enhanced_dialogs import BuildingDialog
            dialog = BuildingDialog(self.root, "إضافة مبنى جديد")
            result = dialog.show()
            if result:
                print("تم إضافة مبنى جديد:", result)
        except ImportError:
            messagebox.showinfo("مبنى جديد", "نافذة إضافة مبنى جديد قيد التطوير")
    
    def test_maintenance_dialog(self):
        """اختبار نافذة بلاغ صيانة"""
        try:
            from enhanced_dialogs import MaintenanceDialog
            dialog = MaintenanceDialog(self.root, "بلاغ صيانة جديد")
            result = dialog.show()
            if result:
                print("تم إرسال بلاغ صيانة:", result)
        except ImportError:
            messagebox.showinfo("بلاغ صيانة", "نافذة بلاغ الصيانة قيد التطوير")

if __name__ == "__main__":
    print("🚀 تشغيل نظام إدارة أعمال الإدارة الهندسية...")
    app = EngineeringManagementApp()
    app.start()