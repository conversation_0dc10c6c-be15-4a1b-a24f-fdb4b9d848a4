#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام المحسن مع شاشة ترحيب
Enhanced System Launcher with Welcome Screen
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import time
import threading

# إضافة المسار الحالي لـ Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

class WelcomeScreen:
    """شاشة الترحيب المحسنة"""
    
    def __init__(self):
        self.window = None
        self.progress_var = None
        self.setup_fonts()
    
    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 24, "bold")
        self.subtitle_font = ("Segoe UI", 16)
        self.normal_font = ("Segoe UI", 14)
        self.small_font = ("Segoe UI", 12)
    
    def show(self):
        """عرض شاشة الترحيب"""
        self.window = ttk_bs.Window(
            title="🏗️ نظام إدارة أعمال الإدارة الهندسية - الإصدار المحسن",
            themename="cosmo",
            size=(800, 600)
        )
        
        # توسيط النافذة
        self.center_window()
        
        # منع تغيير الحجم
        self.window.resizable(False, False)
        
        self.create_welcome_interface()
        self.start_loading()
        
        self.window.mainloop()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_welcome_interface(self):
        """إنشاء واجهة الترحيب"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window, padding=40)
        main_frame.pack(fill=BOTH, expand=True)
        
        # شعار وعنوان النظام
        header_frame = ttk_bs.Frame(main_frame)
        header_frame.pack(fill=X, pady=(0, 30))
        
        # العنوان الرئيسي
        title_label = ttk_bs.Label(
            header_frame,
            text="🏗️ نظام إدارة أعمال الإدارة الهندسية 🏗️",
            font=self.title_font,
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack(pady=(0, 10))
        
        # العنوان الفرعي
        subtitle_label = ttk_bs.Label(
            header_frame,
            text="الإصدار المحسن - Enhanced Version 2.0",
            font=self.subtitle_font,
            bootstyle="secondary"
        )
        subtitle_label.pack()
        
        # إطار التحسينات
        features_frame = ttk_bs.LabelFrame(
            main_frame,
            text="✨ التحسينات الجديدة",
            padding=25,
            bootstyle="info"
        )
        features_frame.pack(fill=X, pady=20)
        
        features_text = """
🎯 جميع الشاشات تفتح في وسط الشاشة
📝 خطوط أكبر وأوضح للقراءة السهلة
🎨 ألوان وأشكال مميزة وجذابة
⏰ تذييل محسن مع الوقت والتاريخ المباشر
💙 خط أزرق غامق وثقيل للوضوح
🖥️ واجهة مستخدم محسنة بالكامل
🚀 أداء محسن وسرعة استجابة أفضل
        """
        
        features_label = ttk_bs.Label(
            features_frame,
            text=features_text,
            font=self.normal_font,
            foreground="#1e3a8a",
            justify=LEFT
        )
        features_label.pack(anchor=W)
        
        # إطار التحميل
        loading_frame = ttk_bs.LabelFrame(
            main_frame,
            text="🔄 جاري التحميل",
            padding=20,
            bootstyle="success"
        )
        loading_frame.pack(fill=X, pady=20)
        
        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk_bs.Progressbar(
            loading_frame,
            variable=self.progress_var,
            maximum=100,
            bootstyle="success-striped",
            length=500
        )
        progress_bar.pack(pady=10)
        
        # نص التحميل
        self.loading_label = ttk_bs.Label(
            loading_frame,
            text="جاري تحضير النظام...",
            font=self.normal_font,
            bootstyle="success"
        )
        self.loading_label.pack(pady=5)
        
        # معلومات إضافية
        info_frame = ttk_bs.Frame(main_frame)
        info_frame.pack(fill=X, pady=(20, 0))
        
        info_label = ttk_bs.Label(
            info_frame,
            text="👨‍💻 تطوير: فريق التطوير المتخصص | 📅 2024",
            font=self.small_font,
            bootstyle="secondary"
        )
        info_label.pack()
    
    def start_loading(self):
        """بدء عملية التحميل"""
        def loading_process():
            steps = [
                ("جاري تحميل الوحدات الأساسية...", 20),
                ("جاري تحضير قاعدة البيانات...", 40),
                ("جاري تطبيق التحسينات...", 60),
                ("جاري تحميل الواجهة المحسنة...", 80),
                ("جاري الانتهاء من التحضير...", 100)
            ]
            
            for step_text, progress in steps:
                self.loading_label.config(text=step_text)
                self.progress_var.set(progress)
                self.window.update()
                time.sleep(1.5)
            
            # إغلاق شاشة الترحيب وتشغيل النظام
            self.window.after(500, self.launch_main_system)
        
        # تشغيل التحميل في خيط منفصل
        loading_thread = threading.Thread(target=loading_process)
        loading_thread.daemon = True
        loading_thread.start()
    
    def launch_main_system(self):
        """تشغيل النظام الرئيسي"""
        try:
            self.window.destroy()
            from main_gui import MainApplication
            app = MainApplication()
            app.start()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل النظام: {str(e)}")

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 بدء تشغيل نظام إدارة أعمال الإدارة الهندسية المحسن...")
        welcome = WelcomeScreen()
        welcome.show()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        messagebox.showerror("خطأ", f"فشل في تشغيل النظام: {str(e)}")

if __name__ == "__main__":
    main()